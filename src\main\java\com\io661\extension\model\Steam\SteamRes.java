package com.io661.extension.model.Steam;

import lombok.Data;
import lombok.Getter;

import java.util.List;


@Data
public class SteamRes {
    /**
     * 响应码
     */
    private int code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     * -- GETTER --
     *  获取Data对象
     */
    @Getter
    private Data_ data;

    /**
     * 获取Steam绑定列表
     * @return Steam绑定列表
     */
    public List<SteamBind> getSteamBindList() {
        return data != null ? data.steamBindList : null;
    }

    @Data
    public static class Data_ {
        /**
         * 绑定的steam列表
         */
        private List<SteamBind> steamBindList;
    }

    @Getter
    @Data
    public static class SteamBind {
        /**
         * SteamId
         * -- GETTER --
         *  获取SteamId
         */
        private String steamId;
        /**
         * 头像地址
         * -- GETTER --
         *  获取头像地址
         */
        private String avatar;
        /**
         * Steam昵称
         * -- GETTER --
         *  获取昵称
         */
        private String nickname;
        /**
         * 交易链接
         * -- GETTER --
         *  获取交易链接
         */
        private String tradeUrl;
        /**
         * 是否可用
         * -- GETTER --
         *  获取是否可用
         */
        private boolean effective;
        /**
         * 是否为急速发货账号
         * -- GETTER --
         *  获取是否为急速发货账号
         */
        private boolean quick;

    }
}
