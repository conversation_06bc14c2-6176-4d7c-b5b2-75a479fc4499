package com.io661.extension.util.Buff;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Properties;

/**
 * Buff cookie 管理类
 * 专门用于管理 buff.163.com 的 session cookie
 */
public class BuffCookieManager {
    // 应用程序数据目录
    private static final String APP_DATA_FOLDER = getAppDataFolder();
    private static final String DATA_FOLDER = APP_DATA_FOLDER + File.separator + "data";
    private static final String BUFF_COOKIE_FILE = DATA_FOLDER + File.separator + "buff_cookie.properties";
    private static final String SESSION_KEY = "session";

    // 确保目录存在
    static {
        File folder = new File(DATA_FOLDER);
        if (!folder.exists()) {
            boolean created = folder.mkdirs();
            System.out.println("创建数据目录: " + (created ? "成功" : "失败") + " - " + DATA_FOLDER);
        }
    }

    /**
     * 获取应用程序数据目录
     * 优先使用应用程序目录，如果无法写入则使用用户主目录
     */
    private static String getAppDataFolder() {
        // 首先尝试使用应用程序目录
        String appPath = System.getProperty("user.dir");
        File appDir = new File(appPath);

        // 检查应用程序目录是否可写
        if (appDir.exists() && appDir.canWrite()) {
            System.out.println("使用应用程序目录存储数据: " + appPath);
            return appPath;
        }

        // 如果应用程序目录不可写，使用用户主目录
        String userHome = System.getProperty("user.home") + File.separator + ".io661";
        System.out.println("使用用户主目录存储数据: " + userHome);
        return userHome;
    }

    /**
     * 保存Buff session cookie到本地文件
     *
     * @param sessionCookie Buff session cookie
     * @return 是否保存成功
     */
    public static boolean saveBuffCookie(String sessionCookie) {
        if (sessionCookie == null || sessionCookie.isEmpty()) {
            System.err.println("Buff session cookie为空，无法保存");
            return false;
        }

        // 确保目录存在
        File folder = new File(DATA_FOLDER);
        if (!folder.exists()) {
            boolean created = folder.mkdirs();
            System.out.println("创建数据目录: " + (created ? "成功" : "失败") + " - " + DATA_FOLDER);
            if (!created) {
                System.err.println("创建目录失败: " + DATA_FOLDER);
                return false;
            }
        }

        // 检查目录是否可写
        if (!folder.canWrite()) {
            System.err.println("目录不可写: " + DATA_FOLDER);
            return false;
        }

        Properties properties = new Properties();
        properties.setProperty(SESSION_KEY, sessionCookie);

        File cookieFile = new File(BUFF_COOKIE_FILE);
        System.out.println("准备保存Buff session cookie到文件: " + cookieFile.getAbsolutePath());

        try (FileWriter writer = new FileWriter(cookieFile)) {
            properties.store(writer, "Buff Session Cookie");
            System.out.println("Buff session cookie已成功保存到: " + cookieFile.getAbsolutePath());
            return true;
        } catch (IOException e) {
            System.err.println("保存Buff session cookie失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 从本地文件读取Buff session cookie
     *
     * @return Buff session cookie，如果不存在则返回null
     */
    public static String readBuffCookie() {
        File file = new File(BUFF_COOKIE_FILE);
        System.out.println("尝试从文件读取Buff session cookie: " + file.getAbsolutePath());

        if (!file.exists()) {
            System.out.println("Buff cookie文件不存在: " + file.getAbsolutePath());
            return null;
        }

        if (!file.canRead()) {
            System.err.println("Buff cookie文件不可读: " + file.getAbsolutePath());
            return null;
        }

        Properties properties = new Properties();
        try (FileReader reader = new FileReader(file)) {
            properties.load(reader);
            String sessionCookie = properties.getProperty(SESSION_KEY);

            if (sessionCookie == null || sessionCookie.isEmpty()) {
                System.out.println("Buff cookie文件中没有有效的session cookie");
                return null;
            }

            System.out.println("成功从文件读取到Buff session cookie: " + sessionCookie.substring(0, Math.min(10, sessionCookie.length())) + "...");
            return sessionCookie;
        } catch (IOException e) {
            System.err.println("读取Buff session cookie失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 删除本地保存的Buff session cookie
     *
     * @return 是否删除成功
     */
    public static boolean deleteBuffCookie() {
        File file = new File(BUFF_COOKIE_FILE);
        System.out.println("尝试删除Buff cookie文件: " + file.getAbsolutePath());

        if (!file.exists()) {
            System.out.println("Buff cookie文件不存在，无需删除");
            return true;
        }

        if (!file.canWrite()) {
            System.err.println("Buff cookie文件不可写，无法删除: " + file.getAbsolutePath());
            return false;
        }

        boolean result = file.delete();
        System.out.println("删除Buff cookie文件: " + (result ? "成功" : "失败") + " - " + file.getAbsolutePath());
        return result;
    }

    /**
     * 检查Buff cookie文件是否存在
     *
     * @return 如果Buff cookie文件存在且可读返回true，否则返回false
     */
    public static boolean buffCookieFileExists() {
        File file = new File(BUFF_COOKIE_FILE);
        boolean exists = file.exists() && file.canRead();
        System.out.println("检查Buff cookie文件是否存在: " + (exists ? "存在" : "不存在") + " - " + file.getAbsolutePath());
        return exists;
    }

    /**
     * 获取Buff cookie文件路径
     *
     * @return Buff cookie文件的完整路径
     */
    public static String getBuffCookieFilePath() {
        return new File(BUFF_COOKIE_FILE).getAbsolutePath();
    }
}
