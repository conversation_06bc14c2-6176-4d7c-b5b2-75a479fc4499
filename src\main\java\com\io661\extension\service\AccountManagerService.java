package com.io661.extension.service;

import com.io661.extension.model.Steam.SteamRes;
import com.io661.extension.model.Steam.SteamTokenRes;

import java.util.List;

public interface AccountManagerService {
    /**
     * 新增steam账号（Ma文件绑定）
     */
    public void addSteamAccountByMaFile(String maFile);

    /**
     * 获取所有的steam账号
     */
    List<SteamRes.SteamBind> getAllSteamAccount(String token);

    /**
     * 获取Steam账号的令牌
     * @param steamId Steam账号ID
     * @param token 授权令牌
     * @return Steam令牌响应
     */
    SteamTokenRes getSteamToken(String steamId, String token);

    /**
     * 绑定Steam令牌（通过.maFile文件）
     * @param maFileContent .maFile文件内容
     * @param password 密码
     * @param token 授权令牌
     * @return 是否绑定成功
     */
    boolean bindSteamTokenByMaFile(String maFileContent, String password, String token);

    /**
     * 解绑Steam令牌
     * @param steamId Steam账号ID
     * @param token 授权令牌
     * @return 是否解绑成功
     */
    boolean unbindSteamToken(String steamId, String token);

    /**
     * 用户在浏览器登陆后用获取cookie后用base64加密后
     * @param cookies 授权令牌
     * @return 是否登录成功
     */
    boolean loginSteamAccount(String cookies);
}
