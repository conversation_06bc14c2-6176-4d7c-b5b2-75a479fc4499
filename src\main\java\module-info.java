module com.io661.extension {
    requires javafx.controls;
    requires javafx.fxml;
    requires javafx.base;
    requires javafx.graphics;
    requires javafx.web;
    requires jdk.jsobject;

    requires org.controlsfx.controls;
    requires com.dlsc.formsfx;
    requires org.kordamp.bootstrapfx.core;
    requires static lombok;
    requires java.desktop;
    requires java.net.http;
    requires com.google.gson;
    requires org.seleniumhq.selenium.edge_driver;
    requires org.seleniumhq.selenium.devtools_v134;
    requires java.sql;

    opens com.io661.extension to javafx.fxml;
    exports com.io661.extension;

    exports com.io661.extension.controller;
    opens com.io661.extension.controller to javafx.fxml;

    opens com.io661.extension.commonResult to javafx.fxml;
    exports com.io661.extension.commonResult;

    exports com.io661.extension.model.User;
    opens com.io661.extension.model.User to javafx.fxml;

    exports com.io661.extension.model.Login;
    opens com.io661.extension.model.Login to com.google.gson;

    exports com.io661.extension.commonURL;
    opens com.io661.extension.commonURL to javafx.fxml;

    exports com.io661.extension.model.Steam;
    opens com.io661.extension.model.Steam to com.google.gson;

    exports com.io661.extension.service;
    opens com.io661.extension.service to javafx.fxml;

    exports com.io661.extension.model.YouPin;
    opens com.io661.extension.model.YouPin to com.google.gson;

    exports com.io661.extension.model.Enum;
    opens com.io661.extension.model.Enum to com.google.gson;

    opens com.io661.extension.model to com.google.gson;
}