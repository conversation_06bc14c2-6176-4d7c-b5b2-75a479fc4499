<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<VBox xmlns:fx="http://javafx.com/fxml/1" xmlns="http://javafx.com/javafx/21"
      fx:controller="com.io661.extension.controller.SteamController"
      styleClass="password-dialog" spacing="15" alignment="CENTER"
      style="-fx-background-color: white; -fx-padding: 20px; -fx-border-color: #ddd; -fx-border-width: 1px;">

    <HBox alignment="CENTER_RIGHT">
        <Label text="输入密码" style="-fx-font-size: 16px; -fx-font-weight: bold;" HBox.hgrow="ALWAYS" alignment="CENTER"/>
        <Button fx:id="closeButton" text="×" onAction="#handleClose"
                style="-fx-background-color: transparent; -fx-font-size: 16px; -fx-cursor: hand;"/>
    </HBox>

    <HBox alignment="CENTER" spacing="10">
        <Label text="请输入Steam账号密码" style="-fx-font-size: 14px;" HBox.hgrow="ALWAYS"/>
        <ImageView fitHeight="24.0" fitWidth="24.0">
            <Image url="@../img/help.png" />
        </ImageView>
    </HBox>

    <HBox alignment="CENTER" spacing="5">
        <Label text="密码:" style="-fx-font-size: 14px;"/>
        <PasswordField fx:id="passwordField" promptText="输入密码" prefWidth="200"/>
    </HBox>

    <HBox alignment="CENTER" spacing="10">
        <Button fx:id="confirmButton" text="确定" onAction="#handleConfirm"
                style="-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-padding: 5 15; -fx-cursor: hand;"/>
        <Button fx:id="cancelButton" text="取消" onAction="#handleClose"
                style="-fx-background-color: #f0f0f0; -fx-padding: 5 15; -fx-cursor: hand;"/>
    </HBox>
</VBox>
