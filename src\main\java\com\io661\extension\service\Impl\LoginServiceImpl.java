package com.io661.extension.service.Impl;

import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.service.LoginService;

import java.io.IOException;


public class LoginServiceImpl implements LoginService {
    private final CommonHttpUrl httpClient;

    public LoginServiceImpl() {
        this.httpClient = new CommonHttpUrl();
    }


    @Override
    // 登录方法
    public String login(String phone, String code) {
        try {
            // 构建JSON请求体，包含phone、code、isLongLogin参数
            String jsonBody = String.format("{\"phone\":\"%s\",\"code\":\"%s\",\"remember\":%b}", phone, code, true);

            // 发送POST请求到指定的URL，参数在JSON请求体中
            String endpoint = "web/user";
            String response = httpClient.sendRequestWithToken(endpoint, "POST", jsonBody);

            System.out.println("登录请求接口: " + endpoint);
            System.out.println("登录请求参数: " + jsonBody);
            System.out.println("服务器响应: " + response);

            return response;
        } catch (IOException e) {
            System.err.println("登录失败: " + e.getMessage());
        }

        return "";
    }

    @Override
    // 发送验证码
    public boolean sendCode(String phone) {
        try {
            // 构建JSON请求体，包含phone和type参数
            String jsonBody = String.format("{\"phone\":\"%s\",\"type\":0}", phone);

            // 发送POST请求到指定的URL，参数在JSON请求体中
            String endpoint = "web/sms";
            String response = httpClient.sendRequestWithToken(endpoint, "POST", jsonBody);

            System.out.println("发送验证码接口: " + endpoint);
            System.out.println("发送验证码到手机号: " + phone);
            System.out.println("服务器响应: " + response);

            // 根据响应判断是否发送成功
            // 这里简单返回true，实际应用中应该解析响应判断是否成功
            return true;
        } catch (IOException e) {
            System.err.println("发送验证码失败: " + e.getMessage());
            return false;
        }
    }
}

