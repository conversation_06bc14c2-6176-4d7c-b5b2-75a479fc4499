package com.io661.extension.model.Steam;

import lombok.Data;

import java.util.List;

/**
 * 市场上架请求模型
 */
@Data
public class MarketListingRequest {
    /**
     * 库存ID列表
     */
    private List<InventoryItem> inventoryList;
    
    /**
     * 页码
     */
    private String page;
    
    /**
     * Steam ID
     */
    private String steamId;
    
    /**
     * 库存物品模型
     */
    @Data
    public static class InventoryItem {
        /**
         * 库存ID
         */
        private String id;
        
        /**
         * 价格（分）
         */
        private Integer price;
    }
}
