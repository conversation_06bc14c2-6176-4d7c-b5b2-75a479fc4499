package com.io661.extension.controller;

import com.io661.extension.model.SteamAccount;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.control.Label;
import javafx.scene.control.TableView;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import lombok.Data;
import lombok.Getter;

import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;

@Data
public class MainWindowController implements Initializable {
    /**
     * -- GETTER --
     * 获取MainWindowController实例
     */
    @Getter
    private static MainWindowController instance;


    @FXML
    public Label versionCode;
    @FXML
    public VBox accountDetailsPane;
    @FXML
    public Label mainWindowLogo;
    @FXML
    private StackPane contentArea;
    @FXML
    private TableView<SteamAccount> accountTable;
    @FXML
    private Label statusLabel;

    // 菜单项
    @FXML
    private VBox accountManagementMenu;
    @FXML
    private VBox transactionAssistantMenu;
    @FXML
    private VBox personalSettingMenu;
    @FXML
    private VBox contactUsMenu;

    void setVersionCode(String versionCode) {
        this.versionCode.setText("1.0.0");
    }


    @FXML
    private void showAccountManagement() {
        try {
            // 更新选中的菜单项
            updateSelectedMenu(accountManagementMenu);

            // 加载新的账号管理界面
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/account-manager.fxml"));
            Parent accountManagementRoot = loader.load();

            // 清空内容区域并添加新界面
            contentArea.getChildren().clear();
            contentArea.getChildren().add(accountManagementRoot);

            // 更新状态标签
            if (statusLabel != null) {
                statusLabel.setText("账号管理");
            }
        } catch (IOException e) {
            System.err.println("加载账号管理界面失败: " + e.getMessage());

            // 显示账号表格作为备用
            if (accountTable != null) {
                accountTable.setVisible(true);
            }

            // 更新状态标签
            if (statusLabel != null) {
                statusLabel.setText("账号管理");
            }
        }
    }

    @FXML
    private void showTransactionAssistant() {
        try {
            // 更新选中的菜单项
            updateSelectedMenu(transactionAssistantMenu);

            // 加载新的交易助手界面
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/transactionAssistant.fxml"));
            Parent transactionAssistantRoot = loader.load();
            contentArea.getChildren().clear();
            contentArea.getChildren().add(transactionAssistantRoot);

            if (statusLabel != null) {
                statusLabel.setText("交易助手");
            }

        } catch (Exception e) {
            System.err.println("加载交易助手界面失败: " + e.getMessage());

            // 隐藏账号表格
            if (accountTable != null) {
                accountTable.setVisible(false);
            }

            if (statusLabel != null) {
                statusLabel.setText("交易助手");
            }
        }
    }

    @FXML
    private void personalSetting() {
        try {
            // 更新选中的菜单项
            updateSelectedMenu(personalSettingMenu);

            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/personalSetting.fxml"));
            Parent personalSettingRoot = loader.load();

            contentArea.getChildren().clear();
            contentArea.getChildren().add(personalSettingRoot);

            if (statusLabel != null) {
                statusLabel.setText("个人设置");
            }

        } catch (Exception e) {
            System.err.println("加载个人设置界面失败: " + e.getMessage());

            // 隐藏账号表格
            if (accountTable != null) {
                accountTable.setVisible(false);
            }

            if (statusLabel != null) {
                statusLabel.setText("个人设置");
            }
        }
    }

    @FXML
    private void contactUs() {
        try {
            // 更新选中的菜单项
            updateSelectedMenu(contactUsMenu);

            // 加载联系我们界面
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/contactUs.fxml"));
            Parent contactUsRoot = loader.load();

            contentArea.getChildren().clear();
            contentArea.getChildren().add(contactUsRoot);

            if (statusLabel != null) {
                statusLabel.setText("联系我们");
            }
        } catch (Exception e) {
            System.err.println("加载联系我们界面失败: " + e.getMessage());

            // 隐藏账号表格
            if (accountTable != null) {
                accountTable.setVisible(false);
            }

            if (statusLabel != null) {
                statusLabel.setText("联系我们");
            }
        }
    }

    @FXML
    public void expandBTnOnAction(ActionEvent event) {
        if (accountDetailsPane.isVisible()) {
            accountDetailsPane.setVisible(false);
        }
    }

    /**
     * 更新选中的菜单项
     * @param selectedMenu 选中的菜单项
     */
    private void updateSelectedMenu(VBox selectedMenu) {
        // 移除所有菜单项的选中样式
        accountManagementMenu.getStyleClass().remove("selected");
        transactionAssistantMenu.getStyleClass().remove("selected");
        personalSettingMenu.getStyleClass().remove("selected");
        contactUsMenu.getStyleClass().remove("selected");

        // 为选中的菜单项添加选中样式
        if (selectedMenu != null) {
            selectedMenu.getStyleClass().add("selected");
        }
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // 保存实例引用
        instance = this;

        // 设置版本号
        setVersionCode("1.0.0");

        // 初始化时隐藏主界面内容，等登录成功后再显示
        if (contentArea != null) {
            contentArea.setVisible(false);
        }
    }

    /**
     * 登录成功后更新UI
     */
    public void updateAfterLogin() {
        Platform.runLater(() -> {
            // 显示主界面内容
            if (contentArea != null) {
                contentArea.setVisible(true);
            }

            // 默认显示账号管理页面
            showAccountManagement();

            // 可以在这里添加其他登录后的UI更新逻辑
            System.out.println("主界面已更新，用户登录成功");
        });
    }
}
