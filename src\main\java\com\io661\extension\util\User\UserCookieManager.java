package com.io661.extension.util.User;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.net.*;
import java.util.Properties;

/**
 * 用户cookie管理
 */
public class UserCookieManager {
    // 应用程序数据目录
    private static final String APP_DATA_FOLDER = getAppDataFolder();
    private static final String TOKEN_FOLDER = APP_DATA_FOLDER + File.separator + "data";
    private static final String TOKEN_FILE = TOKEN_FOLDER + File.separator + "token.properties";
    private static final String TOKEN_KEY = "authorization";

    // 确保目录存在
    static {
        File folder = new File(TOKEN_FOLDER);
        if (!folder.exists()) {
            boolean created = folder.mkdirs();
            System.out.println("创建数据目录: " + (created ? "成功" : "失败") + " - " + TOKEN_FOLDER);
        }
    }

    /**
     * 获取应用程序数据目录
     * 优先使用应用程序目录，如果无法写入则使用用户主目录
     */
    private static String getAppDataFolder() {
        // 首先尝试使用应用程序目录
        String appPath = System.getProperty("user.dir");
        File appDir = new File(appPath);

        // 检查应用程序目录是否可写
        if (appDir.exists() && appDir.canWrite()) {
            System.out.println("使用应用程序目录存储数据: " + appPath);
            return appPath;
        }

        // 如果应用程序目录不可写，使用用户主目录
        String userHome = System.getProperty("user.home") + File.separator + ".io661";
        System.out.println("使用用户主目录存储数据: " + userHome);
        return userHome;
    }

    /**
     * 保存授权令牌到本地文件
     *
     * @param token 授权令牌
     * @return 是否保存成功
     */
    public static boolean saveToken(String token) {
        if (token == null || token.isEmpty()) {
            System.err.println("授权令牌为空，无法保存");
            return false;
        }

        // 确保目录存在
        File folder = new File(TOKEN_FOLDER);
        if (!folder.exists()) {
            boolean created = folder.mkdirs();
            System.out.println("创建令牌目录: " + (created ? "成功" : "失败") + " - " + TOKEN_FOLDER);
            if (!created) {
                System.err.println("创建目录失败: " + TOKEN_FOLDER);
                return false;
            }
        }

        // 检查目录是否可写
        if (!folder.canWrite()) {
            System.err.println("目录不可写: " + TOKEN_FOLDER);
            return false;
        }

        Properties properties = new Properties();
        properties.setProperty(TOKEN_KEY, token);

        File tokenFile = new File(TOKEN_FILE);
        System.out.println("准备保存令牌到文件: " + tokenFile.getAbsolutePath());

        try (FileWriter writer = new FileWriter(tokenFile)) {
            properties.store(writer, "Authorization Token");
            System.out.println("授权令牌已成功保存到: " + tokenFile.getAbsolutePath());
            return true;
        } catch (IOException e) {
            System.err.println("保存令牌失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 从本地文件读取授权令牌
     *
     * @return 授权令牌，如果不存在则返回null
     */
    public static String readToken() {
        File file = new File(TOKEN_FILE);
        System.out.println("尝试从文件读取令牌: " + file.getAbsolutePath());

        if (!file.exists()) {
            System.out.println("令牌文件不存在: " + file.getAbsolutePath());
            return null;
        }

        if (!file.canRead()) {
            System.err.println("令牌文件不可读: " + file.getAbsolutePath());
            return null;
        }

        Properties properties = new Properties();
        try (FileReader reader = new FileReader(file)) {
            properties.load(reader);
            String token = properties.getProperty(TOKEN_KEY);

            if (token == null || token.isEmpty()) {
                System.out.println("令牌文件中没有有效的令牌");
                return null;
            }

            System.out.println("成功从文件读取到授权令牌: " + token.substring(0, Math.min(10, token.length())) + "...");
            return token;
        } catch (IOException e) {
            System.err.println("读取令牌失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 删除本地保存的授权令牌
     *
     * @return 是否删除成功
     */
    public static boolean deleteToken() {
        File file = new File(TOKEN_FILE);
        System.out.println("尝试删除令牌文件: " + file.getAbsolutePath());

        if (!file.exists()) {
            System.out.println("令牌文件不存在，无需删除");
            return true;
        }

        if (!file.canWrite()) {
            System.err.println("令牌文件不可写，无法删除: " + file.getAbsolutePath());
            return false;
        }

        boolean result = file.delete();
        System.out.println("删除令牌文件: " + (result ? "成功" : "失败") + " - " + file.getAbsolutePath());
        return result;
    }

    /**
     * 设置cookie
     *
     * @param cookie cookie
     */
    public void setCookie(String cookie) throws IOException {
        try {
            String BASE_URL = "https://io661.com/";
            URI uri = new URI(BASE_URL);
            //创建一个CookieManager对象
            CookieManager cookieManager = new CookieManager();
            cookieManager.setCookiePolicy(CookiePolicy.ACCEPT_ORIGINAL_SERVER);
            CookieHandler.setDefault(cookieManager);
            CookieStore cookieStore = cookieManager.getCookieStore();
            URL url = uri.toURL();
            //新建一个cookie
            HttpCookie httpCookie = new HttpCookie("Authorization", cookie);
            cookieStore.add(url.toURI(), httpCookie);
            System.out.println(cookieStore.get(url.toURI()));
        } catch (IOException | URISyntaxException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 清除cookie
     *
     * @return 清除结果
     */
    public boolean clearCookie() throws IOException {
        try {
            URI uri = new URI("https://io661.com/");
            CookieManager cookieManager = new CookieManager();
            cookieManager.setCookiePolicy(CookiePolicy.ACCEPT_ORIGINAL_SERVER);
            CookieHandler.setDefault(cookieManager);
            CookieStore cookieStore = cookieManager.getCookieStore();
            URL url = uri.toURL();
            cookieStore.remove(url.toURI(), cookieManager.getCookieStore().getCookies().getFirst());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查令牌文件是否存在
     *
     * @return 如果令牌文件存在且可读返回true，否则返回false
     */
    public static boolean tokenFileExists() {
        File file = new File(TOKEN_FILE);
        boolean exists = file.exists() && file.canRead();
        System.out.println("检查令牌文件是否存在: " + (exists ? "存在" : "不存在") + " - " + file.getAbsolutePath());
        return exists;
    }

    /**
     * 获取令牌文件路径
     *
     * @return 令牌文件的完整路径
     */
    public static String getTokenFilePath() {
        return new File(TOKEN_FILE).getAbsolutePath();
    }
}
