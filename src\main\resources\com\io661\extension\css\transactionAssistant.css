/* 全局样式 */
.root {
    -fx-background-color: #f5f5f5;
    -fx-font-family: "Segoe UI", Arial, sans-serif;
}

/* 主容器样式 */
.main-container {
    -fx-background-color: white;
}

/* 左侧用户信息区域 */
.user-info-container {
    -fx-background-color: #f4f4f4;
    -fx-padding: 10px;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0 1px 0 0;
}

.user-info-header {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-padding: 5px 0;
}

.user-card {
    -fx-background-color: #f0f0f0;
    -fx-background-radius: 8px;
    -fx-padding: 10px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 3, 0, 0, 1);
}

.username-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.balance-label {
    -fx-font-size: 12px;
    -fx-text-fill: #4caf50;
    -fx-font-weight: bold;
}

.user-info-label {
    -fx-font-size: 12px;
    -fx-text-fill: #757575;
}

.user-info-value {
    -fx-font-size: 12px;
    -fx-text-fill: #333333;
}

.refresh-button {
    -fx-background-color: transparent;
    -fx-padding: 4px;
    -fx-cursor: hand;
}

.refresh-button:hover {
    -fx-background-color: #e0e0e0;
    -fx-background-radius: 50%;
}

/* 导航按钮样式 */
.nav-button {
    -fx-background-color: white;
    -fx-text-fill: #333333;
    -fx-font-size: 14px;
    -fx-padding: 10px 20px;
    -fx-cursor: hand;
    -fx-border-width: 0 0 2px 0;
    -fx-border-color: transparent;
}

.nav-button:hover {
    -fx-background-color: #f5f5f5;
}

.nav-button.active {
    -fx-border-color: #ff5722;
    -fx-text-fill: #ff5722;
    -fx-font-weight: bold;
}

/* 筛选区域样式 */
.filter-container {
    -fx-background-color: white;
    -fx-padding: 10px;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0 0 1px 0;
}

.platform-selector {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-padding: 5px 10px;
}

.filter-checkbox {
    -fx-font-size: 12px;
}

.search-field {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-padding: 5px 10px;
}

.filter-button {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333333;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-padding: 5px 10px;
    -fx-cursor: hand;
}

.filter-button:hover {
    -fx-background-color: #e5e5e5;
}

.filter-button.primary {
    -fx-background-color: #4a90e2;
    -fx-text-fill: white;
    -fx-border-color: #3a80d2;
}

.filter-button.primary:hover {
    -fx-background-color: #3a80d2;
}

/* 排序选择器样式 */
.sort-selector {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-padding: 5px 10px;
}

/* 账号选择器样式 */
.account-selector {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-padding: 5px 10px;
    -fx-font-size: 12px;
}

/* 物品卡片样式 */
.item-card {
    -fx-background-color: white;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 3, 0, 0, 1);
    -fx-padding: 10px;
}

.item-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 5, 0, 0, 2);
}

.item-image-container {
    -fx-background-color: #f5f5f5;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
}

.item-name {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.item-price {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #ff5722;
}

.item-id {
    -fx-font-size: 10px;
    -fx-text-fill: #757575;
}

.item-tag {
    -fx-background-color: #4caf50;
    -fx-text-fill: white;
    -fx-font-size: 10px;
    -fx-padding: 2px 5px;
    -fx-background-radius: 2px;
}

.item-tag.orange {
    -fx-background-color: #ff9800;
}

.item-tag.red {
    -fx-background-color: #f44336;
}

.item-tag.blue {
    -fx-background-color: #2196f3;
}

.item-tag.purple {
    -fx-background-color: #9c27b0;
}

/* 磨损值标签样式 */
.wear-value-label {
    -fx-font-size: 10px;
    -fx-text-fill: #333333;
    -fx-padding: 0 0 0 5px;
}

/* 物品磨损值样式 */
.item-wear {
    -fx-font-size: 10px;
    -fx-text-fill: #666666;
    -fx-padding: 0;
    -fx-alignment: center-right;
}

/* 标签容器样式 */
.tag-container {
    -fx-spacing: 5;
    -fx-padding: 0;
}

/* 标签样式 */
.tag-label {
    -fx-font-size: 10px;
    -fx-text-fill: white;
    -fx-padding: 2px 5px;
    -fx-background-radius: 2px;
}

.stattrak-tag {
    -fx-background-color: #ff9800; /* 橙色 */
}

.tradable-tag {
    -fx-background-color: #4caf50; /* 绿色 */
}

.locked-tag {
    -fx-background-color: #f44336; /* 红色 */
}

.on-sale-tag {
    -fx-background-color: #2196f3; /* 蓝色 */
}

/* 印花容器样式 */
.stickers-container {
    -fx-background-color: transparent;
    -fx-padding: 2px;
}

.sticker-container {
    -fx-background-color: transparent;
    -fx-padding: 1px;
}

/* 分页控件样式 */
.pagination {
    -fx-page-information-visible: false;
}

.pagination .page {
    -fx-background-color: transparent;
}

.pagination .pagination-control .button {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
}

.pagination .pagination-control .button:hover {
    -fx-background-color: #f5f5f5;
}

.pagination .pagination-control .number-button {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
}

.pagination .pagination-control .number-button:hover {
    -fx-background-color: #f5f5f5;
}

.pagination .pagination-control .number-button:selected {
    -fx-background-color: #4a90e2;
    -fx-text-fill: white;
}

/* 滚动面板样式 */
.scroll-pane {
    -fx-background-color: transparent;
    -fx-padding: 10px;
}

.scroll-pane > .viewport {
    -fx-background-color: transparent;
}

.scroll-pane .scroll-bar:vertical {
    -fx-background-color: transparent;
    -fx-padding: 0 0 0 5px;
}

.scroll-pane .scroll-bar:horizontal {
    -fx-background-color: transparent;
    -fx-padding: 5px 0 0 0;
}