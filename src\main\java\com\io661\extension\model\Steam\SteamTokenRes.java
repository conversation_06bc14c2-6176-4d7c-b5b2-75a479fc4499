package com.io661.extension.model.Steam;

import lombok.Data;

import java.util.Map;

/**
 * Steam令牌响应模型
 */
@Data
public class SteamTokenRes {
    /**
     * 响应码
     */
    private int code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     */
    private TokenData data;

    @Data
    public static class TokenData {
        /**
         * 令牌映射，键为令牌代码，值为过期时间戳
         */
        private Map<String, Long> tokenMap;
    }
}
