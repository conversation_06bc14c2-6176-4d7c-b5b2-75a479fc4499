/* 悠悠有品登录对话框样式 */
.youpin-login-dialog {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 10, 0, 0, 3);
}

.dialog-title {
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
}

.input-label {
    -fx-text-fill: #555555;
    -fx-font-size: 12px;
}

.input-field {
    -fx-background-color: white;
    -fx-border-color: #dcdcdc;
    -fx-border-radius: 4px;
    -fx-padding: 8px 12px;
    -fx-font-size: 14px;
}

.input-field:focused {
    -fx-border-color: #27ae60;
    -fx-effect: dropshadow(gaussian, rgba(39, 174, 96, 0.3), 3, 0, 0, 0);
}

.send-code-button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 6px 12px;
    -fx-font-size: 12px;
    -fx-cursor: hand;
}

.send-code-button:hover {
    -fx-background-color: #2980b9;
}

.send-code-button:disabled {
    -fx-background-color: #bdc3c7;
    -fx-cursor: default;
}

.primary-button {
    -fx-background-color: #27ae60;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 8px 20px;
    -fx-font-size: 14px;
    -fx-cursor: hand;
    -fx-min-width: 80px;
}

.primary-button:hover {
    -fx-background-color: #229954;
}

.secondary-button {
    -fx-background-color: #95a5a6;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 8px 20px;
    -fx-font-size: 14px;
    -fx-cursor: hand;
    -fx-min-width: 80px;
}

.secondary-button:hover {
    -fx-background-color: #7f8c8d;
}

.sms-prompt-box {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #27ae60;
    -fx-border-radius: 4px;
    -fx-padding: 15px;
}

.sms-prompt-text {
    -fx-text-fill: #2c3e50;
    -fx-font-size: 13px;
    -fx-line-spacing: 2px;
}
