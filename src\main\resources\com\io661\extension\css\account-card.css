/* 账号卡片样式 */
.account-card {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 5, 0, 0, 2);
    -fx-transition: -fx-effect 0.3s;
}

.account-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 8, 0, 0, 3);
}

/* 小型账号卡片样式 */
.account-mini-card {
    -fx-background-color: white;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 3, 0, 0, 1);
    -fx-transition: -fx-effect 0.2s;
    -fx-cursor: hand;
}

.account-mini-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 5, 0, 0, 2);
    -fx-background-color: #f5f5f5;
}

.account-mini-card.selected {
    -fx-border-color: #4a90e2;
    -fx-border-width: 2px;
}

/* 头像样式 */
.avatar-container {
    -fx-min-width: 40px;
    -fx-min-height: 40px;
    -fx-max-width: 40px;
    -fx-max-height: 40px;
}

.avatar-mini-container {
    -fx-min-width: 30px;
    -fx-min-height: 30px;
    -fx-max-width: 30px;
    -fx-max-height: 30px;
}

.avatar-circle {
    -fx-fill: #f0f0f0;
    -fx-stroke: #e0e0e0;
    -fx-stroke-width: 1px;
}

/* 用户名和余额样式 */
.username-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.balance-label {
    -fx-font-size: 12px;
    -fx-text-fill: #4caf50;
    -fx-font-weight: bold;
}

.account-status-label {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.account-status-label.effective {
    -fx-text-fill: #4caf50; /* 绿色 - 有效 */
}

.account-status-label.ineffective {
    -fx-text-fill: #f44336; /* 红色 - 无效 */
}

.username-mini-label {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.balance-mini-label {
    -fx-font-size: 10px;
    -fx-text-fill: #4caf50;
    -fx-font-weight: bold;
}


/* 卡片图标按钮样式 */
.card-icon-button {
    -fx-background-color: transparent;
    -fx-padding: 4px;
    -fx-cursor: hand;
}

.card-icon-button:hover {
    -fx-background-color: #f5f5f5;
    -fx-background-radius: 50%;
}

/* 分隔线样式 */
.card-separator {
    -fx-opacity: 0.5;
}

/* 信息标签样式 */
.info-label {
    -fx-font-size: 12px;
    -fx-text-fill: #757575;
}

.info-value {
    -fx-font-size: 12px;
    -fx-text-fill: #333333;
}

/* 复制按钮样式 */
.copy-button {
    -fx-background-color: transparent;
    -fx-padding: 2px;
    -fx-cursor: hand;
}

.copy-button:hover {
    -fx-background-color: #f5f5f5;
    -fx-background-radius: 50%;
}

/* 代理状态样式 */
.proxy-status-circle {
    -fx-fill: #4caf50; /* 默认为绿色，表示正常 */
}

.proxy-status-circle.warning {
    -fx-fill: #ff9800;
}

.proxy-status-circle.error {
    -fx-fill: #f44336;
}

.proxy-status-label {
    -fx-font-size: 12px;
    -fx-text-fill: #333333;
}

/* 信息按钮样式 */
.info-button {
    -fx-background-color: transparent;
    -fx-padding: 2px;
    -fx-cursor: hand;
}

.info-button:hover {
    -fx-background-color: #f5f5f5;
    -fx-background-radius: 50%;
}

/* 卡片操作按钮样式 */
.card-action-button {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333333;
    -fx-padding: 8px 0;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
    -fx-font-size: 12px;
}

.card-action-button:hover {
    -fx-background-color: #e5e5e5;
}

.card-action-button.login-button {
    -fx-background-color: #4a90e2;
    -fx-text-fill: white;
}

.card-action-button.login-button:hover {
    -fx-background-color: #3a80d2;
}
