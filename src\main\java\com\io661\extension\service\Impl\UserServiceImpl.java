package com.io661.extension.service.Impl;

import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.service.UserService;
import lombok.Data;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Data
public class UserServiceImpl implements UserService {
    private final CommonHttpUrl httpClient;

    public UserServiceImpl() {
        this.httpClient = new CommonHttpUrl();
    }

    @Override
    public boolean validateToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            System.out.println("令牌为空，无法验证");
            return false;
        }
        try {
            System.out.println("正在验证令牌: " + token.substring(0, Math.min(20, token.length())) + "...");
            String userInfo = getUserInfo(token);

            // 检查响应是否包含成功代码
            boolean isValid = userInfo != null && userInfo.contains("\"code\":0");
            System.out.println("令牌验证结果: " + (isValid ? "有效" : "无效"));

            return isValid;
        } catch (Exception e) {
            System.err.println("验证令牌失败: " + e.getMessage());
            return false;
        }
    }

    @Override
    public String getUserInfo(String token) {
        if (token == null || token.trim().isEmpty()) {
            System.out.println("令牌为空，无法获取用户信息");
            return null;
        }

        try {
            // 临时设置授权令牌
            httpClient.setAuthToken(token);
            System.out.println("已设置授权令牌，准备获取用户信息");

            // 创建请求头
            Map<String, String> headers = new HashMap<>();

            headers.put("Cookie", "Authorization=" + token);
            System.out.println("已添加Authorization Cookie: " + token.substring(0, Math.min(20, token.length())) + "...");

            // 发送GET请求到用户信息接口
            String endpoint = "web/user";
            System.out.println("发送请求到: " + httpClient.getBaseUrl() + endpoint);

            // 直接使用doGet方法，确保请求头被正确设置
            String response = httpClient.doGet(endpoint, null, headers);

            // 打印响应的前100个字符，避免日志过长
            String logResponse = response;
            if (response != null && response.length() > 100) {
                logResponse = response.substring(0, 100) + "...";
            }
            System.out.println("获取用户信息响应: " + logResponse);

            return response;
        } catch (IOException e) {
            System.err.println("获取用户信息失败: " + e.getMessage());
            return null;
        } finally {
            // 清除临时设置的授权令牌
            httpClient.setAuthToken(null);
        }
    }
}
