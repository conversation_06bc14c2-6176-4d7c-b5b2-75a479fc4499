package com.io661.extension.model.YouPin;
import lombok.Data;

import java.util.List;

@Data
public class YouPinUserInventoryOnSellDataListRes {
    private Integer code;
    private String msg;
    private Long timestamp;
    private Data_ data;

    @Data
    public static class Data_ {
        private List<CommodityInfoList> commodityInfoList;
        private StatisticalData statisticalData;
        private Integer totalPages;
        private Integer csInspectionVersion;
        private AppealConfig appealConfig;
    }

    @Data
    public static class CommodityInfoList {
        private Long id;
        private Integer templateId;
        private String name;
        private String imgUrl;
        private String exteriorName;
        private String simpleExteriorName;
        private String exteriorColor;
        private String rarityName;
        private String simpleRarityName;
        private String rarityColor;
        private String qualityColor;
        private Integer status;
        private String statusDesc;
        private Integer haveNameTag;
        private String abrade;
        private Integer haveSticker;
        private List<Stickers> stickers; // 贴纸列表（可能为空）
        private Integer leaseMaxDays;
        private String sellAmount;
        private String shortLeaseAmount;
        private String longLeaseAmount;
        private String depositAmount;
        private String sellAmountDesc;
        private String clickTipMessage;
        private Integer mergeCommodityCount;
        private String referencePrice;
        private Boolean templateHasZeroLease;
        private Boolean hasEasyCompensationPrivilege;
        private Integer highDepositStatus;
        private String steamAssetId; // 新增字段（JSON中存在）
        private String commodityHashName; // 新增字段（JSON中存在）
        private Integer canLease;
        private Boolean commodityHasZeroLease;
        private Boolean hasLeaseGive;
        private Integer commodityCanSell;
        private Integer commodityCanLease;
        private Integer paintseed; // 注意驼峰命名修正（原JSON为小写）
        private Integer havePendant;
        private List<?> pendants; // 空数组默认用 List<?>
    }

    @Data
    public static class Stickers {
        private Integer rawIndex;
        private String name;
        private String hashName;
        private String imageUrl;
        private String abradeDesc;
    }

    @Data
    public static class StatisticalData {
        private Integer quantity;
        private String statisticalData;
    }

    @Data
    public static class AppealConfig {
        private String tipsTitle;
        private String tipsContent;
        private String appealUrl;
    }
}