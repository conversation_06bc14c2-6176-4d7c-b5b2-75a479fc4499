<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<VBox xmlns:fx="http://javafx.com/fxml/1" xmlns="http://javafx.com/javafx/21"
      fx:controller="com.io661.extension.controller.SteamController"
      styleClass="batch-login-dialog" spacing="15" alignment="CENTER"
      style="-fx-background-color: #333333; -fx-padding: 20px;">

    <HBox alignment="CENTER_LEFT" spacing="10">
        <Button fx:id="backButton" styleClass="back-button">
            <graphic>
                <ImageView fitHeight="16.0" fitWidth="16.0">
                    <Image url="@../img/back.png" />
                </ImageView>
            </graphic>
        </Button>
        <Label text="批量绑定Steam账号" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: white;" HBox.hgrow="ALWAYS" alignment="CENTER"/>
        <Button fx:id="closeButton" text="×"
                style="-fx-background-color: transparent; -fx-font-size: 16px; -fx-text-fill: white; -fx-cursor: hand;"/>
    </HBox>

    <Button fx:id="uploadMaFileButton" text="上传对应账号MaFile文件"
            style="-fx-background-color: #222222; -fx-text-fill: white; -fx-padding: 10; -fx-cursor: hand;" maxWidth="Infinity"/>

    <Label text="上传账号密码表格（请按照："账号-密码"格式输入，多个账号用回车分割）"
           style="-fx-font-size: 12px; -fx-text-fill: white;" wrapText="true"/>

    <TextArea fx:id="accountPasswordTextArea" prefHeight="150.0"
              style="-fx-background-color: #222222; -fx-text-fill: white; -fx-control-inner-background: #222222;"/>

    <Label text="注：绑定账号后会自动刷新存储，请在绑定后5分钟后再次刷新存储"
           style="-fx-font-size: 12px; -fx-text-fill: #ff6b6b;" wrapText="true"/>

    <Label text="绑定结果" style="-fx-font-size: 14px; -fx-text-fill: white;"/>

    <TextArea fx:id="resultTextArea" prefHeight="100.0" editable="false"
              style="-fx-background-color: #222222; -fx-text-fill: white; -fx-control-inner-background: #222222;"/>

    <Button fx:id="bindButton" text="绑定"
            style="-fx-background-color: #00bcd4; -fx-text-fill: white; -fx-padding: 10; -fx-cursor: hand;" maxWidth="Infinity"/>
</VBox>
