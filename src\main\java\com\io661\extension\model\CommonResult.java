package com.io661.extension.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 通用响应结果
 */
@Data
@Accessors(chain = true)
public class CommonResult<T> {
    /**
     * 响应码
     */
    private int code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 创建成功响应
     */
    public static <T> CommonResult<T> webSuccess(String message, T data) {
        return new CommonResult<T>()
                .setCode(0)
                .setMsg(message)
                .setData(data);
    }

    /**
     * 创建失败响应
     */
    public static <T> CommonResult<T> webFail(String message) {
        return new CommonResult<T>()
                .setCode(-1)
                .setMsg(message);
    }
}
