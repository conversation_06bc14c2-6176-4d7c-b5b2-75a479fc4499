<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.Circle?>
<VBox xmlns:fx="http://javafx.com/fxml/1" styleClass="account-card" stylesheets="@../../css/account-card-new.css" xmlns="http://javafx.com/javafx/21">
    <padding>
        <Insets bottom="8" left="8" right="8" top="8" />
    </padding>

    <!-- 顶部信息区域 -->
    <HBox alignment="CENTER_LEFT" spacing="10">
        <!-- 头像 -->
        <StackPane styleClass="avatar-container">
            <Circle radius="15" styleClass="avatar-circle" />
            <ImageView fx:id="avatarImageView" fitHeight="30" fitWidth="30" pickOnBounds="true" preserveRatio="true">
                <Image url="@../../img/avatar.png" />
            </ImageView>
        </StackPane>

        <!-- 用户名和余额 -->
        <VBox spacing="2">
            <HBox alignment="CENTER_LEFT" spacing="5">
                <Label fx:id="usernameLabel" styleClass="username-label" text="baconcat" />
                <ImageView fitHeight="12.0" fitWidth="12.0">
                    <Image url="@../../img/steam.png" />
                </ImageView>
            </HBox>
            <Label fx:id="balanceLabel" styleClass="balance-label" text="HK$ 0.00" />
        </VBox>

        <Region HBox.hgrow="ALWAYS" />

        <!-- 刷新按钮 -->
        <Button mnemonicParsing="false" styleClass="card-icon-button">
            <graphic>
                <ImageView fitHeight="14.0" fitWidth="14.0">
                    <Image url="@../../img/refresh.png" />
                </ImageView>
            </graphic>
        </Button>

        <!-- 设置按钮 -->
        <Button mnemonicParsing="false" styleClass="card-icon-button">
            <graphic>
                <ImageView fitHeight="14.0" fitWidth="14.0">
                    <Image url="@../../img/settings.png" />
                </ImageView>
            </graphic>
        </Button>
    </HBox>

    <Label styleClass="time-label" text="距离下个交易时间: 12分钟" />

    <!-- 账号详细信息 -->
    <GridPane hgap="10" vgap="4">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" minWidth="60" prefWidth="60" />
            <ColumnConstraints hgrow="SOMETIMES" minWidth="10" prefWidth="100" />
        </columnConstraints>

        <Label styleClass="info-label" text="ID:" />
        <Label fx:id="steamIdLabel" styleClass="info-value" text="76561198960007194" GridPane.columnIndex="1" />

        <Label styleClass="info-label" text="交易URL:" GridPane.rowIndex="1" />
        <HBox alignment="CENTER_LEFT" spacing="5" GridPane.columnIndex="1" GridPane.rowIndex="1">
            <Label fx:id="tradeUrlLabel" styleClass="info-value" text="...partner=1019741368&amp;token=..." />
            <Button mnemonicParsing="false" styleClass="copy-button">
                <graphic>
                    <ImageView fitHeight="10.0" fitWidth="10.0">
                        <Image url="@../../img/copy.png" />
                    </ImageView>
                </graphic>
            </Button>
        </HBox>

        <Label styleClass="info-label" text="IP配置:" GridPane.rowIndex="2" />
        <HBox alignment="CENTER_LEFT" spacing="5" GridPane.columnIndex="1" GridPane.rowIndex="2">
            <Label styleClass="info-value" text="内置代理" />
            <Button mnemonicParsing="false" styleClass="dropdown-button">
                <graphic>
                    <ImageView fitHeight="10.0" fitWidth="10.0">
                        <Image url="@../../img/dropdown.png" />
                    </ImageView>
                </graphic>
            </Button>
        </HBox>
        <rowConstraints>
            <RowConstraints />
            <RowConstraints />
            <RowConstraints />
        </rowConstraints>
    </GridPane>

    <!-- 底部按钮 -->
    <HBox alignment="CENTER" spacing="10">
        <VBox.margin>
            <Insets top="5" />
        </VBox.margin>
        <Button maxWidth="Infinity" styleClass="card-action-button" text="查看令牌" HBox.hgrow="ALWAYS" />
    </HBox>
</VBox>
