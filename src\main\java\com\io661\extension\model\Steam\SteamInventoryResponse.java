package com.io661.extension.model.Steam;

import lombok.Data;

import java.util.List;

@Data
public class SteamInventoryResponse {
    /**
     * 响应码
     */
    private int code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     */
    private ResponseData data;

    @Data
    public static class ResponseData {
        /**
         * 总页数
         */
        private int pages;

        /**
         * 总记录数
         */
        private int total;

        /**
         * 总数量
         */
        private int count;

        /**
         * 物品列表
         */
        private List<SteamInventoryRes> list;
    }
}
