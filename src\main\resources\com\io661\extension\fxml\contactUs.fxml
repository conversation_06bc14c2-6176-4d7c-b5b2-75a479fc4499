<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<ScrollPane xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1">
   <content>
      <VBox spacing="20.0">
         <padding>
            <Insets bottom="30.0" left="30.0" right="30.0" top="30.0" />
         </padding>
         <children>
            <!-- 标题 -->
            <Label text="联系我们" styleClass="title-label">
               <font>
                  <Font size="24.0" />
               </font>
            </Label>
            
            <!-- 联系信息 -->
            <VBox spacing="15.0">
               <children>
                  <Label text="客服支持" styleClass="section-title">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </Label>
                  
                  <GridPane hgap="20.0" vgap="15.0">
                     <columnConstraints>
                        <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                        <ColumnConstraints hgrow="SOMETIMES" minWidth="300.0" />
                     </columnConstraints>
                     <rowConstraints>
                        <RowConstraints minHeight="30.0" vgrow="NEVER" />
                        <RowConstraints minHeight="30.0" vgrow="NEVER" />
                        <RowConstraints minHeight="30.0" vgrow="NEVER" />
                        <RowConstraints minHeight="30.0" vgrow="NEVER" />
                     </rowConstraints>
                     <children>
                        <Label text="官方网站:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label text="https://io661.com" styleClass="link-label" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="客服邮箱:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label text="<EMAIL>" styleClass="link-label" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="QQ群:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Label text="123456789" styleClass="link-label" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="工作时间:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <Label text="周一至周五 9:00-18:00" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                     </children>
                  </GridPane>
               </children>
            </VBox>
            
            <!-- 分隔线 -->
            <Separator />
            
            <!-- 反馈建议 -->
            <VBox spacing="15.0">
               <children>
                  <Label text="意见反馈" styleClass="section-title">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </Label>
                  
                  <Label text="如果您在使用过程中遇到问题或有改进建议，请通过以下方式联系我们：" styleClass="description-label" />
                  
                  <VBox spacing="10.0">
                     <children>
                        <Label text="• 发送邮件至 <EMAIL>" styleClass="help-label" />
                        <Label text="• 加入官方QQ群进行交流" styleClass="help-label" />
                        <Label text="• 访问官方网站查看最新公告" styleClass="help-label" />
                     </children>
                  </VBox>
               </children>
            </VBox>
            
            <!-- 版本信息 -->
            <VBox spacing="10.0">
               <children>
                  <Label text="版本信息" styleClass="section-title">
                     <font>
                        <Font size="16.0" />
                     </font>
                  </Label>
                  <Label text="当前版本: 1.0.0" styleClass="help-label" />
                  <Label text="更新时间: 2024-01-01" styleClass="help-label" />
                  <Label text="© 2024 IO661. All rights reserved." styleClass="help-label" />
               </children>
            </VBox>
         </children>
      </VBox>
   </content>
</ScrollPane>
