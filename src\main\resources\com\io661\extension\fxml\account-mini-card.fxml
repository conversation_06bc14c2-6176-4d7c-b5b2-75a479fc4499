<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.Circle?>
<HBox xmlns:fx="http://javafx.com/fxml/1" xmlns="http://javafx.com/javafx/21"
      styleClass="account-mini-card" prefWidth="200" prefHeight="50"
      stylesheets="@../css/account-card.css" alignment="CENTER_LEFT" spacing="8">
    <padding>
        <Insets top="5" right="8" bottom="5" left="8" />
    </padding>

    <!-- 头像 -->
    <StackPane styleClass="avatar-mini-container">
        <Circle fx:id="avatarCircle" radius="15" styleClass="avatar-circle" />
        <ImageView fx:id="avatarImage" fitHeight="30" fitWidth="30" pickOnBounds="true" preserveRatio="true">
            <Image url="@../img/avatar.png" />
        </ImageView>
    </StackPane>

    <!-- 用户名和ID -->
    <VBox spacing="1">
        <Label fx:id="usernameLabel" text="Username" styleClass="username-mini-label" />
        <Label fx:id="balanceLabel" text="¥0.00" styleClass="balance-mini-label" />
    </VBox>

    <Region HBox.hgrow="ALWAYS" />

    <!-- 状态指示 -->
    <Circle fx:id="statusCircle" radius="4" styleClass="proxy-status-circle" />
</HBox>
