/* 全局样式 */
.root {
    -fx-background-color: #f5f5f5;
    -fx-font-family: "Segoe UI", Arial, sans-serif;
}

/* 标题样式 */
.title-label {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

/* 搜索框样式 */
.search-box {
    -fx-background-color: white;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-padding: 2px 8px;
}

.search-field {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 6px 0;
}

.icon-button {
    -fx-background-color: transparent;
    -fx-cursor: hand;
}

.icon-button:hover {
    -fx-opacity: 0.8;
}

/* 主要按钮样式 */
.primary-button {
    -fx-background-color: #4a90e2;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 8px 16px;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
}

.primary-button:hover {
    -fx-background-color: #3a80d2;
}

.primary-button:pressed {
    -fx-background-color: #2a70c2;
}

/* 次要按钮样式 */
.secondary-button {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333333;
    -fx-padding: 8px 16px;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
}

.secondary-button:hover {
    -fx-background-color: #e5e5e5;
}

.secondary-button:pressed {
    -fx-background-color: #d5d5d5;
}

/* 菜单按钮样式 */
.menu-button {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333333;
    -fx-padding: 8px 16px;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
}

.menu-button:hover {
    -fx-background-color: #e5e5e5;
}

.menu-button:showing {
    -fx-background-color: #d5d5d5;
}

/* 过滤栏样式 */
.filter-bar {
    -fx-background-color: white;
    -fx-padding: 8px 12px;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
}

/* 视图切换按钮样式 */
.view-toggle {
    -fx-background-color: transparent;
    -fx-padding: 4px;
    -fx-cursor: hand;
}

.view-toggle:selected {
    -fx-background-color: #e0e0e0;
    -fx-background-radius: 4px;
}

/* 卡片容器样式 */
.cards-container {
    -fx-padding: 10px;
}

/* 透明滚动面板 */
.transparent-scroll-pane {
    -fx-background-color: transparent;
    -fx-background: transparent;
    -fx-border-color: transparent;
}

.transparent-scroll-pane > .viewport {
    -fx-background-color: transparent;
}

/* 状态栏样式 */
.status-bar {
    -fx-background-color: white;
    -fx-padding: 10px;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
}

.online-count {
    -fx-text-fill: #4caf50;
    -fx-font-weight: bold;
}

.offline-count {
    -fx-text-fill: #f44336;
    -fx-font-weight: bold;
}

/* 弹出层样式 */
.popup-overlay {
    -fx-background-color: rgba(0, 0, 0, 0.5);
}

.popup-content {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-padding: 20px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 10, 0, 0, 5);
}

.popup-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
}

.close-button {
    -fx-background-color: transparent;
    -fx-cursor: hand;
}

.close-button:hover {
    -fx-background-color: #f0f0f0;
    -fx-background-radius: 50%;
}

.popup-button {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333333;
    -fx-padding: 12px 16px;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
}

.popup-button:hover {
    -fx-background-color: #e5e5e5;
}

.cancel-button {
    -fx-background-color: #f5f5f5;
    -fx-text-fill: #757575;
    -fx-padding: 12px 16px;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
}

.cancel-button:hover {
    -fx-background-color: #eeeeee;
}
