package com.io661.extension.util.Steam;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * Utility class for managing Steam cookies
 */
public class SteamCookieManager {

    private static final String COOKIES_FOLDER = System.getProperty("user.home") + File.separator + ".io661" + File.separator + "steam";
    private static final String COOKIES_FILE = COOKIES_FOLDER + File.separator + "cookies.json";

    /**
     * Get the path to the cookies file
     */
    public static String getCookiesFilePath() {
        return COOKIES_FILE;
    }

    /**
     * Check if the cookies file exists
     */
    public static boolean cookiesFileExists() {
        return new File(COOKIES_FILE).exists();
    }

    /**
     * Save cookies to a file
     */
    public static boolean saveCookies(Map<String, Map<String, String>> cookieMap) {
        if (cookieMap == null || cookieMap.isEmpty()) {
            System.err.println("Cookie map is empty, cannot save");
            return false;
        }

        // Ensure the directory exists
        File folder = new File(COOKIES_FOLDER);
        if (!folder.exists()) {
            boolean created = folder.mkdirs();
            System.out.println("Created cookies directory: " + (created ? "success" : "failed") + " - " + COOKIES_FOLDER);
            if (!created) {
                System.err.println("Failed to create directory: " + COOKIES_FOLDER);
                return false;
            }
        }

        // Check if the directory is writable
        if (!folder.canWrite()) {
            System.err.println("Directory is not writable: " + COOKIES_FOLDER);
            return false;
        }

        // Convert the cookie map to JSON
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        String json = gson.toJson(cookieMap);

        File cookiesFile = new File(COOKIES_FILE);
        System.out.println("Saving cookies to file: " + cookiesFile.getAbsolutePath());

        try (FileWriter writer = new FileWriter(cookiesFile)) {
            writer.write(json);
            System.out.println("Cookies successfully saved to: " + cookiesFile.getAbsolutePath());
            return true;
        } catch (IOException e) {
            System.err.println("Failed to save cookies: " + e.getMessage());
            return false;
        }
    }

    /**
     * Read cookies from a file
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Map<String, String>> readCookies() {
        File file = new File(COOKIES_FILE);
        System.out.println("Attempting to read cookies from file: " + file.getAbsolutePath());

        if (!file.exists()) {
            System.out.println("Cookies file does not exist: " + file.getAbsolutePath());
            return createEmptyCookieMap();
        }

        if (!file.canRead()) {
            System.err.println("Cookies file is not readable: " + file.getAbsolutePath());
            return createEmptyCookieMap();
        }

        try {
            String content = new String(Files.readAllBytes(Paths.get(file.getAbsolutePath())));
            Gson gson = new Gson();
            Map<String, Map<String, String>> cookieMap = gson.fromJson(content, Map.class);
            System.out.println("Successfully read cookies from file");
            return cookieMap;
        } catch (IOException e) {
            System.err.println("Failed to read cookies: " + e.getMessage());
            return createEmptyCookieMap();
        }
    }

    /**
     * Delete the cookies file
     */
    public static boolean deleteCookies() {
        File file = new File(COOKIES_FILE);
        if (file.exists()) {
            boolean deleted = file.delete();
            System.out.println("Deleted cookies file: " + (deleted ? "success" : "failed"));
            return deleted;
        }
        return true;
    }

    /**
     * Create an empty cookie map with the required structure
     */
    public static Map<String, Map<String, String>> createEmptyCookieMap() {
        Map<String, Map<String, String>> cookieMap = new HashMap<>();
        cookieMap.put("api.steampowered.com", new HashMap<>());
        cookieMap.put("login.steampowered.com", new HashMap<>());
        cookieMap.put("store.steampowered.com", new HashMap<>());
        cookieMap.put("steamcommunity.com", new HashMap<>());
        cookieMap.put("help.steampowered.com", new HashMap<>());
        cookieMap.put("checkout.steampowered.com", new HashMap<>());
        return cookieMap;
    }

    /**
     * Convert cookie map to BASE64 and print to console
     *
     * @param cookieMap The cookie map to convert
     */
    public static void printCookiesAsBase64(Map<String, Map<String, String>> cookieMap) {
        if (cookieMap == null) {
            System.err.println("Cookie map is null, cannot convert to BASE64");
            return;
        }

        // Check if all domains have empty cookie maps
        boolean allEmpty = true;
        for (Map<String, String> domainCookies : cookieMap.values()) {
            if (domainCookies != null && !domainCookies.isEmpty()) {
                allEmpty = false;
                break;
            }
        }

        if (cookieMap.isEmpty() || allEmpty) {
            System.out.println("Warning: Cookie map is empty or contains no cookies");
            System.out.println("Proceeding with empty cookie map...");
        }

        try {
            // Convert the cookie map to JSON
            Gson gson = new GsonBuilder().create();
            String json = gson.toJson(cookieMap);

            // Print the JSON for debugging
            System.out.println("Cookie JSON structure:");
            System.out.println(json);

            // Convert JSON to BASE64
            String base64Cookies = Base64.getEncoder().encodeToString(json.getBytes());

            // Print the BASE64 string to console
            System.out.println("\nSteam Cookies (BASE64):");
            System.out.println(base64Cookies);

            // Print the length for verification
            System.out.println("\nBASE64 string length: " + base64Cookies.length());
        } catch (Exception e) {
            System.err.println("Failed to convert cookies to BASE64: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
