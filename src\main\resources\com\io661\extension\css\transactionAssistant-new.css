/* 全局样式 */
.root {
    -fx-background-color: #f5f5f5;
    -fx-font-family: "Segoe UI", Arial, sans-serif;
}

/* 主容器样式 */
.main-container {
    -fx-background-color: white;
}

/* 左侧用户信息区域 */
.user-info-container {
    -fx-background-color: #f4f4f4;
    -fx-padding: 10px;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0 1px 0 0;
}

.user-info-header {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-padding: 5px 0;
}

/* 搜索框样式 */
.search-box {
    -fx-background-color: white;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
}

.search-field {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 5px;
    -fx-font-size: 12px;
}

.icon-button {
    -fx-background-color: transparent;
    -fx-padding: 4px;
    -fx-cursor: hand;
}

.icon-button:hover {
    -fx-background-color: #f0f0f0;
    -fx-background-radius: 50%;
}

/* 账号分组标题 */
.account-group-header {
    -fx-padding: 5px 0;
}

.expand-button {
    -fx-background-color: transparent;
    -fx-padding: 2px;
    -fx-cursor: hand;
}

.group-label {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.account-selector-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6200ee;
    -fx-font-weight: bold;
}

/* 标签页样式 */
.tab-container {
    -fx-background-color: #f0f0f0;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0 0 1px 0;
}

.tab-button {
    -fx-background-color: transparent;
    -fx-text-fill: #333333;
    -fx-font-size: 14px;
    -fx-padding: 10px 20px;
    -fx-cursor: hand;
    -fx-border-width: 0 0 3px 0;
    -fx-border-color: transparent;
}

.tab-button:hover {
    -fx-background-color: #e5e5e5;
}

.tab-button.active-tab {
    -fx-border-width: 0 0 3px 0;
    -fx-font-weight: bold;
}

/* 为每个标签设置不同的颜色 */
#inventoryButton.active-tab {
    -fx-border-color: #9c27b0; /* 紫色 */
    -fx-text-fill: #9c27b0;
}

#onSaleButton.active-tab {
    -fx-border-color: #ff9800; /* 橙色 */
    -fx-text-fill: #ff9800;
}

#confirmButton.active-tab {
    -fx-border-color: #2196f3; /* 蓝色 */
    -fx-text-fill: #2196f3;
}

#priceButton.active-tab {
    -fx-border-color: #4caf50; /* 绿色 */
    -fx-text-fill: #4caf50;
}

.tab-button.focused-tab {
    -fx-border-color: #ff9800;
    -fx-border-width: 0 0 3px 0;
    -fx-effect: dropshadow(gaussian, rgba(255, 152, 0, 0.4), 5, 0, 0, 0);
}

/* 窗口控制按钮 */
.window-control-button {
    -fx-background-color: transparent;
    -fx-padding: 10px 15px;
    -fx-cursor: hand;
    -fx-opacity: 0;  /* 隐藏窗口控制按钮 */
    -fx-visibility: hidden;  /* 完全隐藏按钮 */
}

.window-control-button:hover {
    -fx-background-color: #e0e0e0;
}

.close-button:hover {
    -fx-background-color: #ff5252;
}

/* 筛选区域样式 */
.filter-container {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0 0 1px 0;
}

.search-container {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0 0 1px 0;
}

.platform-checkbox {
    -fx-font-size: 12px;
}

.filter-checkbox {
    -fx-font-size: 12px;
}

.filter-label {
    -fx-font-size: 12px;
    -fx-text-fill: #757575;
}

.filter-button {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333333;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-padding: 5px 10px;
    -fx-cursor: hand;
    -fx-font-size: 12px;
}

.filter-button:hover {
    -fx-background-color: #e5e5e5;
}

.sort-button {
    -fx-background-color: transparent;
    -fx-padding: 4px;
    -fx-cursor: hand;
}

.sort-button:hover {
    -fx-background-color: #f0f0f0;
    -fx-background-radius: 50%;
}

/* 库存信息样式 */
.inventory-info {
    -fx-background-color: #f9f9f9;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0 0 1px 0;
}

.inventory-summary {
    -fx-font-size: 12px;
    -fx-text-fill: #333333;
}

.refresh-button {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333333;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-padding: 3px 8px;
    -fx-cursor: hand;
    -fx-font-size: 12px;
}

.refresh-button:hover {
    -fx-background-color: #e5e5e5;
}

/* 物品卡片样式 */
.item-card {
    -fx-background-color: white;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 3, 0, 0, 1);
    -fx-padding: 10px;
}

.item-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 5, 0, 0, 2);
}

/* 选中物品的样式 */
.item-card.selected-item {
    -fx-background-color: #e3f2fd; /* 浅蓝色背景 */
    -fx-border-color: #2196f3; /* 蓝色边框 */
    -fx-border-width: 2px; /* 加粗边框 */
    -fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.4), 5, 0, 0, 2); /* 蓝色阴影 */
}

.item-card.selected-item:hover {
    -fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.6), 7, 0, 0, 3); /* 更强的蓝色阴影 */
}

.item-tag {
    -fx-padding: 2px 0;
}

.tag-label {
    -fx-font-size: 10px;
    -fx-text-fill: white;
    -fx-padding: 2px 5px;
    -fx-background-radius: 2px;
}

.new-tag {
    -fx-background-color: #ff5722;
}

.normal-tag {
    -fx-background-color: #4caf50;
}

.rare-tag {
    -fx-background-color: #2196f3;
}

.on-sale-tag {
    -fx-background-color: #ff9800;
}

.stattrak-tag {
    -fx-background-color: #ff9800;
}

.locked-tag {
    -fx-background-color: #9c27b0;
}

.tradable-tag {
    -fx-background-color: #4caf50;
}

.quantity-tag {
    -fx-background-color: #2196f3; /* 蓝色 */
    -fx-font-weight: bold;
}

.item-id {
    -fx-font-size: 10px;
    -fx-text-fill: #757575;
    -fx-padding: 2px 0;
}

.item-image-container {
    -fx-background-color: #f5f5f5;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-padding: 5px;
    -fx-alignment: center;
}

.item-name {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
    -fx-padding: 2px 0;
}

.item-type {
    -fx-font-size: 11px;
    -fx-text-fill: #757575;
    -fx-padding: 0;
}

.top-tag-container {
    -fx-padding: 2px 0;
}

.item-action-button {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333333;
    -fx-padding: 3px 10px;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
    -fx-font-size: 12px;
}

.item-action-button:hover {
    -fx-background-color: #e5e5e5;
}

.item-price {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #ff5722;
    -fx-padding: 5px 0 0 0;
}

.empty-message {
    -fx-font-size: 16px;
    -fx-text-fill: #757575;
    -fx-padding: 20px;
    -fx-alignment: center;
}

/* 滚动面板样式 */
.scroll-pane {
    -fx-background-color: transparent;
    -fx-padding: 0;
}

.scroll-pane > .viewport {
    -fx-background-color: transparent;
}

.scroll-pane .scroll-bar:vertical {
    -fx-background-color: transparent;
    -fx-padding: 0 0 0 5px;
}

.scroll-pane .scroll-bar:horizontal {
    -fx-background-color: transparent;
    -fx-padding: 5px 0 0 0;
}

.transparent-scroll-pane {
    -fx-background-color: transparent;
}

.transparent-scroll-pane > .viewport {
    -fx-background-color: transparent;
}
