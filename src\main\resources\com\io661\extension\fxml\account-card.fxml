<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.Circle?>
<VBox xmlns:fx="http://javafx.com/fxml/1" xmlns="http://javafx.com/javafx/21"
      styleClass="account-card" prefWidth="280" prefHeight="220"
      stylesheets="@../css/account-card.css">
    <padding>
        <Insets top="12" right="12" bottom="12" left="12" />
    </padding>

    <!-- 顶部信息区域 -->
    <HBox alignment="CENTER_LEFT" spacing="10">
        <!-- 头像 -->
        <StackPane styleClass="avatar-container">
            <Circle fx:id="avatarCircle" radius="20" styleClass="avatar-circle" />
            <ImageView fx:id="avatarImage" fitHeight="40" fitWidth="40" pickOnBounds="true" preserveRatio="true" >
                <Image url="@../img/avatar.png" />
            </ImageView>
        </StackPane>

        <!-- 用户名和状态 -->
        <VBox spacing="2">
            <HBox alignment="CENTER_LEFT" spacing="5">
                <!-- 机器人/人工图标 -->
                <ImageView fx:id="accountTypeIcon" fitHeight="16.0" fitWidth="16.0" preserveRatio="true">
                    <Image url="@../img/human.png" />
                </ImageView>
                <Label fx:id="usernameLabel" text="Username" styleClass="username-label" />
            </HBox>
            <Label fx:id="balanceLabel" text="账号状态" styleClass="account-status-label" />
        </VBox>

        <Region HBox.hgrow="ALWAYS" />
        <Button fx:id="refreshButton" mnemonicParsing="false" styleClass="card-icon-button">
            <graphic>
                <ImageView fitHeight="16.0" fitWidth="16.0">
                    <Image url="@../img/refresh.png" />
                </ImageView>
            </graphic>
        </Button>

        <Button fx:id="settingsButton" mnemonicParsing="false" styleClass="card-icon-button">
            <graphic>
                <ImageView fitHeight="16.0" fitWidth="16.0">
                    <Image url="@../img/settings.png" />
                </ImageView>
            </graphic>
        </Button>
    </HBox>

    <Separator styleClass="card-separator">
        <VBox.margin>
            <Insets top="8" bottom="8" />
        </VBox.margin>
    </Separator>

    <!-- 账号详细信息 -->
    <GridPane hgap="10" vgap="6">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" minWidth="80" prefWidth="80" />
            <ColumnConstraints hgrow="SOMETIMES" minWidth="10" prefWidth="100" />
        </columnConstraints>

        <Label text="ID:" styleClass="info-label" GridPane.rowIndex="0" GridPane.columnIndex="0" />
        <Label fx:id="accountIdLabel" text="----------" styleClass="info-value"
               GridPane.rowIndex="0" GridPane.columnIndex="1" />

        <Label text="交易URL:" styleClass="info-label" GridPane.rowIndex="1" GridPane.columnIndex="0" />
        <HBox alignment="CENTER_LEFT" spacing="5" GridPane.rowIndex="1" GridPane.columnIndex="1">
            <Label fx:id="tradeUrlLabel" text="...partner=123456&amp;token=ABC" styleClass="info-value" />
            <Button fx:id="copyTradeUrlButton" mnemonicParsing="false" styleClass="copy-button">
                <graphic>
                    <ImageView fitHeight="12.0" fitWidth="12.0">
                        <Image url="@../img/copy.png" />
                    </ImageView>
                </graphic>
            </Button>
        </HBox>

        <Label text="IP配置:" styleClass="info-label" GridPane.rowIndex="2" GridPane.columnIndex="0" />
        <Label fx:id="ipConfigLabel" text="内置代理" styleClass="info-value"
               GridPane.rowIndex="2" GridPane.columnIndex="1" />

        <Label text="代理状态:" styleClass="info-label" GridPane.rowIndex="3" GridPane.columnIndex="0" />
        <HBox alignment="CENTER_LEFT" spacing="5" GridPane.rowIndex="3" GridPane.columnIndex="1">
            <Circle fx:id="proxyStatusCircle" radius="4" styleClass="proxy-status-circle" />
            <Label fx:id="proxyStatusLabel" text="正常" styleClass="proxy-status-label" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="proxyInfoButton" mnemonicParsing="false" styleClass="info-button">
                <graphic>
                    <ImageView fitHeight="12.0" fitWidth="12.0">
                        <Image url="@../img/info.png" />
                    </ImageView>
                </graphic>
            </Button>
        </HBox>

        <Label text="已绑定平台:" styleClass="info-label" GridPane.rowIndex="4" GridPane.columnIndex="0" />
        <HBox fx:id="boundPlatformsBox" alignment="CENTER_LEFT" spacing="5" GridPane.rowIndex="4" GridPane.columnIndex="1">
        </HBox>
    </GridPane>

    <Region VBox.vgrow="ALWAYS" />

    <!-- 底部按钮 -->
    <HBox spacing="10" alignment="CENTER">
        <Button fx:id="viewTokenButton" text="查看令牌" styleClass="card-action-button" maxWidth="Infinity" HBox.hgrow="ALWAYS" />
    </HBox>
</VBox>