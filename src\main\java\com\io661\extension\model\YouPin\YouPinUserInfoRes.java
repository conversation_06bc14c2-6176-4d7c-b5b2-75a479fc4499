package com.io661.extension.model.YouPin;

import com.google.gson.annotations.SerializedName;
import lombok.Data;


@Data
public class YouPinUserInfoRes {
    public Integer Code;

    public String Msg;

    public int TipType;

    public UserInfoData Data;

    @Data
    public static class UserInfoData {

        private Double UserId;
        private String NickName;

        @SerializedName("NickNameModifyTip")
        private String nickNameModifyTip;

        private String Mobile;
        private Double Balance;
        private Double BalanceMoney;
        private Double TotalMoney;
        private Double BlockMoney;
        private Double PurchaseMoney;
        private Double PurchaseBlockMoney;
        private Double UnReadSiteMsgNum;
        private String Avatar;
        private String ApiKey;
        private String TransactionUrl;
        private String RealName;
        private String IdCard;
        private String SteamId;
        private Boolean InventoryStatus;
        private Double OnlyWithDrawMoney;
        private String Area;
        private Double SteamTradeStatus;
        private String TradeStatusExpireTime;

        @SerializedName("SteamTradeStatusTip")
        private String steamTradeStatusTip;

        @SerializedName("TradeStatusRefreshTimeTip")
        private String tradeStatusRefreshTimeTip;

        private String SteamJumpUrl;
    }

}
