package com.io661.extension.commonResult;

import lombok.Data;

@Data
public abstract class CommonResult<T> {
    private Integer code;
    private String msg;
    private T data;

    /**
     * 设置响应码
     * @param code 响应码
     */
    public void setCode(int code) {
        this.code = code;
    }

    public static class Web<T> extends CommonResult<T> {
        protected CommonResult<T> result(Integer code, String msg, T data) {
            // 修复：移除不必要的构造函数调用，直接设置属性
            this.setCode(code);
            this.setMsg(msg);
            this.setData(data);

            return this;
        }
    }

    /**
     * 错误返回
     *
     * @param code 错误码
     * @param msg  错误信息
     */
    public static <T> CommonResult<T> webError(Integer code, String msg, T data) {
        return new Web<T>().result(code, msg, data);
    }

    /**
     * 成功返回
     *
     * @param msg  提示信息
     * @param data 返回内容
     */
    public static <T> CommonResult<T> webSuccess(Integer code, String msg, T data) {
        return new Web<T>().result(code, msg, data);
    }
}
