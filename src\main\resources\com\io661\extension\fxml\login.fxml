<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Hyperlink?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Text?>

<AnchorPane maxHeight="400.0" maxWidth="320.0" minHeight="364.0" minWidth="320.0" prefHeight="364.0" prefWidth="320.0" styleClass="login-pane" stylesheets="@../css/login.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.io661.extension.controller.LoginController">
   <children>
      <VBox alignment="TOP_CENTER" spacing="15" styleClass="login-bg" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <padding>
            <Insets bottom="25" left="25" right="25" top="25" />
         </padding>
         <children>
            <Text styleClass="login-title" text="用户登录" textAlignment="CENTER">
               <VBox.margin>
                  <Insets bottom="25" />
               </VBox.margin>
            </Text>

            <TextField fx:id="phoneText" prefHeight="35.0" promptText="请输入手机号" styleClass="input-field">
               <VBox.margin>
                  <Insets bottom="15" />
               </VBox.margin>
            </TextField>

            <AnchorPane>
               <children>
                  <TextField fx:id="codeText" prefHeight="35.0" promptText="请输入验证码" styleClass="input-field" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
                  <Button fx:id="codeBtn" onAction="#sendCodeBtnOnAction" prefHeight="35.0" prefWidth="80.0" styleClass="code-button-transparent" text="发送验证码" AnchorPane.bottomAnchor="0.0" AnchorPane.rightAnchor="5.0" AnchorPane.topAnchor="0.0" />
               </children>
               <VBox.margin>
                  <Insets bottom="15" />
               </VBox.margin>
            </AnchorPane>

            <Button fx:id="loginBtn" onAction="#loginBtnOnAction" prefHeight="40.0" prefWidth="328.0" styleClass="login-button" text="验证并登录">
               <VBox.margin>
                  <Insets bottom="20" />
               </VBox.margin>
            </Button>

            <HBox alignment="CENTER" styleClass="agreement-container">
               <children>
                  <CheckBox fx:id="agreeCheckBox" onAction="#agreeCheckBoxOnAction" styleClass="agreement-checkbox" />
                  <Label styleClass="agreement-text" text="已阅读并同意" />
                  <Hyperlink onAction="#openServiceAgreement" styleClass="agreement-link" text="《服务协议》" />
                  <Label styleClass="agreement-text" text="和" />
                  <Hyperlink onAction="#openPrivacyPolicy" styleClass="agreement-link" text="《隐私协议》" />
               </children>
               <VBox.margin>
                  <Insets top="5" />
               </VBox.margin>
            </HBox>
         </children>
      </VBox>
   </children>
</AnchorPane>
