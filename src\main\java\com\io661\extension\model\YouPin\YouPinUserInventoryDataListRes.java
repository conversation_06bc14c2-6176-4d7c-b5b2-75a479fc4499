package com.io661.extension.model.YouPin;

import lombok.Data;

import java.util.List;

@Data
public class YouPinUserInventoryDataListRes {
    private Boolean newApi;
    private Integer code;
    private Data_ data;

    @Data
    public static class Data_ {
        private Integer totalCount;
        private String inventoryTotalInfo;
        private PreSaleNotice preSaleNotice;
        private List<ItemsInfo> itemsInfos;
    }

    @Data
    public static class ItemsInfo {
        private String steamAssetId;
        private String assetAddTime;
        private String classId;
        private String instanceId;
        private Boolean tradable;
        private Boolean steamMarketable;
        private String shotName;
        private String actionLink;
        private String actionId;
        private TemplateInfo templateInfo;
        private String marketHashName;
        private List<Tags> tags;
        private Integer haveNameTag;
        private String cacheExpiration;
        private List<Stickers> stickers;
        private Integer stickerType;
        private Integer hasSticker;
        private AssetInfo assetInfo;
        private Integer analysis2dStatus;
        private Boolean isCanAnalysis;
        private Boolean banLease;
        private String assetRemark;
        private Integer hasPrerogative;
        private VipPrerogative vipPrerogative;
        private UltraLongLeaseInfo ultraLongLeaseInfo;
        private Integer isMerge;
        private Integer assetStatus;
        private Integer csInspectionVersion;
        private String assetTagColor;
        private Integer hasPendant;
        private List<?> pendants; // 空数组默认用 List<?>
    }

    @Data
    public static class TemplateInfo {
        private Integer id;
        private Integer gameId;
        private Double markPrice;
        private String showMarkPrice;
        private String commodityName;
        private String commodityHashName;
        private String iconUrl;
        private String iconUrlLarge;
    }

    @Data
    public static class Tags {
        private String category;
        private String internalName;
        private String localizedCategoryName;
        private String localizedTagName;
        private String color; // 非必选字段（部分标签无 color）
        private String shortName; // 非必选字段（部分标签无 shortName）
    }

    @Data
    public static class Stickers {
        private Integer stickerId;
        private Integer rawIndex;
        private String name;
        private String hashName;
        private String material;
        private String imgUrl;
        private String abrade;
        private String steamHashName;
        private Integer templateId;
    }

    @Data
    public static class AssetInfo {
        private Integer paintSeed;
        private Integer paintIndex;
        private Double abrade;
        private String stickers; // 字符串形式的 JSON 数组（原数据中为字符串，如需解析可改为 List<Stickers>）
        private Integer specialPropType;
    }

    @Data
    public static class VipPrerogative {
        private Integer platformCommodity;
        private Integer currentRiskStatus;
        private String normalRate;
        private String discountRate;
        private Integer discountedState;
        private String businessId;
        private Integer orderSubType;
    }

    @Data
    public static class UltraLongLeaseInfo {
        private Integer platformCommodity;
        private Integer currentRiskStatus;
        private String normalRate;
        private String discountRate;
        private Integer discountedState;
        private String businessId;
        private Integer orderSubType;
    }

    @Data
    public static class PreSaleNotice {
        private String presaleTipMsg;
        private String preSellDescUrl;
        private String preSellTitle;
        private String preSellDesc;
        private String preSellModeTitle;
        private String btnText;
        private Integer supportPresale;
    }
}
