<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<VBox xmlns:fx="http://javafx.com/fxml/1" styleClass="item-card" xmlns="http://javafx.com/javafx/21">
    <padding>
        <Insets bottom="8" left="8" right="8" top="8" />
    </padding>

    <!-- 物品标签 -->
    <HBox styleClass="item-tag">
        <Label fx:id="tagLabel" styleClass="tag-label, normal-tag" />
    </HBox>

    <!-- 物品ID -->
    <Label fx:id="itemIdLabel"  styleClass="item-id" />

    <!-- 物品图片 -->
    <StackPane styleClass="item-image-container">
        <ImageView fx:id="itemImageView" fitHeight="120.0" fitWidth="180.0" preserveRatio="true">
            <Image url="@../../img/item-placeholder.png" />
        </ImageView>
    </StackPane>

    <!-- 物品名称 -->
    <Label fx:id="itemNameLabel"  styleClass="item-name" />

    <!-- 物品价格 -->
    <HBox alignment="CENTER_RIGHT">
        <Label fx:id="priceLabel"  styleClass="item-price" />
    </HBox>
</VBox>
