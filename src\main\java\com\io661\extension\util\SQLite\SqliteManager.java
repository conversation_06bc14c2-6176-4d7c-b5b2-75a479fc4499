package com.io661.extension.util.SQLite;

import java.nio.file.Paths;
import java.sql.*;

public class SqliteManager {
    public static String DATABASE_PATH = Paths.get(System.getProperty("user.dir"), "src", "main", "resources", "SQLite", "io661extension.db").toString();
    public static Connection connection;

    static {
        connectDatabase();
    }

    public static void connectDatabase() {
        // 打印尝试连接的路径，方便调试
        System.out.println("链接数据库失败: " + DATABASE_PATH);
        try {
            if (connection == null || connection.isClosed()) {
                connection = DriverManager.getConnection("jdbc:sqlite:" + DATABASE_PATH);
                System.out.println("连接成功: " + DATABASE_PATH);
                // 创建表（如果不存在） - 可选，但对于首次运行很方便
                createTableIfNotExists();
            }
        } catch (SQLException e) {
            System.err.println("连接失败: " + DATABASE_PATH);
            throw new RuntimeException("数据库连接失败路径: " + DATABASE_PATH, e);
        }
    }

    /**
     * 创建 YouPin_user_steam_bind 表（如果它不存在）
     * 同时确保 steam_id 是唯一的，这样我们可以进行 UPSERT 操作或避免重复
     */
    private static void createTableIfNotExists() {
        // 这里的表结构应该与你实际使用的匹配
        // 增加了 UNIQUE 约束到 steam_id 和 NOT NULL 及 DEFAULT 到时间戳列
        String sqlCreateTable = "CREATE TABLE IF NOT EXISTS YouPin_user_steam_bind (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "steam_id TEXT UNIQUE NOT NULL, " +
                "authorization TEXT NOT NULL, " +
                "create_time TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')), " +
                "update_time TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime'))" +
                ");";
        // 触发器用于自动更新 update_time
        String sqlCreateTrigger = "CREATE TRIGGER IF NOT EXISTS trg_youpin_cookie_update_time " +
                "AFTER UPDATE ON YouPin_user_steam_bind " +
                "FOR EACH ROW " +
                "WHEN OLD.authorization IS NOT NEW.authorization " + // 仅当 authorization 变化时更新
                "BEGIN " +
                "    UPDATE YouPin_user_steam_bind SET update_time = (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')) WHERE id = OLD.id; " +
                "END;";

        try (Statement stmt = connection.createStatement()) {
            stmt.execute(sqlCreateTable);
            stmt.execute(sqlCreateTrigger);
        } catch (SQLException e) {
            System.err.println("创建或查询表YouPin_user_steam_bind失败: " + e.getMessage());
            // 如果表结构重要，可以考虑抛出异常
        }
    }


    /**
     * 增加或更新YouPin cookie。
     * 如果指定 steamId 的记录已存在，则更新 authorization 和 update_time。
     * 否则，插入新记录。
     * @param steamId     steamId
     * @param cookieValue authorization
     * @return 是否成功
     */
    public static boolean upsertYouPinCookie(String steamId, String cookieValue) {
        // SQLITE UPSERT (INSERT OR REPLACE or ON CONFLICT DO UPDATE)
        // 使用 ON CONFLICT 需要 steam_id 列有 UNIQUE 约束
        String sql = "INSERT INTO YouPin_user_steam_bind (steam_id, authorization) VALUES (?, ?) " +
                "ON CONFLICT(steam_id) DO UPDATE SET authorization = excluded.authorization, update_time = (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime'));";

        if (steamId == null || steamId.trim().isEmpty()) {
            System.err.println("steamId不能为空");
            return false;
        }
        if (cookieValue == null) { // cookieValue 可以为空字符串，表示清除
            System.err.println("CookieValue不能为空");
            return false;
        }


        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, steamId);
            preparedStatement.setString(2, cookieValue);
            preparedStatement.executeUpdate();
            return true;
        } catch (SQLException e) {
            System.err.println("增加或更新Cookie失败" + steamId + ": " + e.getMessage());
            // 决定是否抛出，对于manager类，返回false可能更友好
            // throw new RuntimeException("Upserting Cookie失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 查询YouPin数据库cookie
     * @param steamId steamId
     * @return cookie, 如果未找到则返回 null
     */
    public static String queryYouPinCookie(String steamId) {
        if (steamId == null || steamId.trim().isEmpty()) {
            System.err.println("steamId不能为空");
            return null;
        }
        String sql = "SELECT authorization FROM YouPin_user_steam_bind WHERE steam_id = ?";
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, steamId);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("authorization");
                }
            }
        } catch (SQLException e) {
            System.err.println("查询Cookie失败" + steamId + ": " + e.getMessage());
            // throw new RuntimeException("查询Cookie失败: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 删除指定 steamId 的 YouPin cookie
     * @param steamId steamId
     * @return 是否成功删除 (如果记录存在并被删除则为 true)
     */
    public static boolean deleteYouPinCookie(String steamId) {
        if (steamId == null || steamId.trim().isEmpty()) {
            System.err.println("steamId不能为空");
            return false;
        }
        String sql = "DELETE FROM YouPin_user_steam_bind WHERE steam_id = ?";
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, steamId);
            int rowsAffected = preparedStatement.executeUpdate();
            return rowsAffected > 0;
        } catch (SQLException e) {
            System.err.println("删除Cookie失败" + steamId + ": " + e.getMessage());
            // throw new RuntimeException("删除Cookie失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查指定 steamId 的 YouPin cookie 是否存在
     * @param steamId steamId
     * @return 如果存在则为 true, 否则为 false
     */
    public static boolean cookieExists(String steamId) {
        return queryYouPinCookie(steamId) != null;
    }
}