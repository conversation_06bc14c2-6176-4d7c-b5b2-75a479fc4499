<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<BorderPane xmlns:fx="http://javafx.com/fxml/1" prefHeight="180.0" prefWidth="300.0"
            xmlns="http://javafx.com/javafx/21"
            fx:controller="com.io661.extension.controller.SteamController"
            styleClass="token-dialog" style="-fx-background-color: #333333;"
            stylesheets="@../css/token-dialog.css">
    <top>
        <HBox alignment="CENTER" style="-fx-padding: 10px;">
            <Label text="查看令牌" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: white;"/>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="closeButton" text="×" onAction="#handleClose"
                    style="-fx-background-color: transparent; -fx-font-size: 16px; -fx-cursor: hand; -fx-text-fill: white;"/>
        </HBox>
    </top>
    <center>
        <VBox alignment="CENTER" spacing="15" style="-fx-padding: 10px;">
            <VBox style="-fx-background-color: #222222; -fx-padding: 15px; -fx-background-radius: 5;">
                <Label fx:id="accountNameLabel" text="baconcat" style="-fx-font-size: 14px; -fx-text-fill: white;" alignment="CENTER" maxWidth="Infinity" textAlignment="CENTER"/>

                <StackPane alignment="CENTER">
                    <Label fx:id="tokenLabel" text="R8DDW" style="-fx-font-size: 36px; -fx-font-weight: bold; -fx-text-fill: white;" alignment="CENTER" maxWidth="Infinity" textAlignment="CENTER" onMouseClicked="#handleCopy" styleClass="token-label" />
                    <Button fx:id="copyButton" onAction="#handleCopy" style="-fx-background-color: transparent; -fx-cursor: hand;" StackPane.alignment="CENTER_RIGHT">
                        <graphic>
                            <Region style="-fx-background-image: url('/com/io661/extension/img/copy.png'); -fx-background-size: 16px; -fx-background-repeat: no-repeat; -fx-min-width: 16px; -fx-min-height: 16px;"/>
                        </graphic>
                    </Button>
                </StackPane>

                <ProgressBar fx:id="tokenProgressBar" progress="0.7" prefWidth="250.0" style="-fx-accent: #4CAF50;" maxWidth="Infinity"/>
            </VBox>
        </VBox>
    </center>
</BorderPane>
