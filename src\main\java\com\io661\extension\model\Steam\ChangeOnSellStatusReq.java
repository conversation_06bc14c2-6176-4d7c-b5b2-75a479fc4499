package com.io661.extension.model.Steam;

import lombok.Data;

import java.util.List;

/**
 * 上下架状态变更请求模型
 */
@Data
public class ChangeOnSellStatusReq {
    /**
     * 库存物品列表
     */
    private List<Inventory> inventoryList;
    
    /**
     * 库存物品模型
     */
    @Data
    public static class Inventory {
        /**
         * 库存ID
         */
        private Long id;
        
        /**
         * 价格（分）
         */
        private Integer price;

        /**
         * Steam的assetId
         */
        private Long assetId;

    }
}
