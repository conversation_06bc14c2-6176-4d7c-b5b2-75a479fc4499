<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<BorderPane xmlns:fx="http://javafx.com/fxml/1" prefHeight="500.0" prefWidth="600.0"
            xmlns="http://javafx.com/javafx/21"
            fx:controller="com.io661.extension.controller.MarketListingDialogController"
            styleClass="market-listing-dialog"
            stylesheets="@../css/market-listing-dialog.css">
    <top>
        <HBox alignment="CENTER_LEFT" spacing="10" styleClass="dialog-header">
            <Label text="Steam出售" styleClass="dialog-title"/>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="closeButton" text="×" onAction="#handleClose"
                    styleClass="close-button"/>
            <padding>
                <Insets bottom="10" left="15" right="15" top="10" />
            </padding>
        </HBox>
    </top>

    <center>
        <VBox spacing="15" styleClass="dialog-content">
            <padding>
                <Insets bottom="15" left="15" right="15" top="15" />
            </padding>

            <!-- 饰品信息 -->
            <Label text="饰品由于价格存在差异，请仔细检验价格后确认上架" styleClass="warning-text"/>

            <!-- 选择上架平台 -->
            <VBox spacing="10" styleClass="platform-selection-container">
                <Label text="选择上架平台" styleClass="section-title"/>
                <HBox spacing="15" alignment="CENTER_LEFT">
                    <CheckBox fx:id="io661Checkbox" text="IO661" selected="true" disable="true"/>
                    <CheckBox fx:id="uuCheckbox" text="UU"/>
                    <CheckBox fx:id="buffCheckbox" text="BUFF"/>
                </HBox>
            </VBox>

            <!-- 饰品列表 -->
            <VBox spacing="10" VBox.vgrow="ALWAYS" styleClass="items-container">
                <Label text="饰品列表" styleClass="section-title"/>
                <ScrollPane fitToWidth="true" styleClass="items-scroll-pane" VBox.vgrow="ALWAYS">
                    <VBox fx:id="itemsContainer" spacing="5" styleClass="items-list">
                        <!-- 物品将在控制器中动态添加 -->
                    </VBox>
                </ScrollPane>
            </VBox>
        </VBox>
    </center>

    <bottom>
        <HBox alignment="CENTER_RIGHT" spacing="10" styleClass="dialog-footer">
            <Button fx:id="cancelButton" text="取消" onAction="#handleCancel" styleClass="cancel-button"/>
            <Button fx:id="confirmButton" text="确定上架" onAction="#handleConfirm" styleClass="confirm-button"/>
            <padding>
                <Insets bottom="15" left="15" right="15" top="15" />
            </padding>
        </HBox>
    </bottom>
</BorderPane>
