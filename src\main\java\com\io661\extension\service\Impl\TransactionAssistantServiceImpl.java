package com.io661.extension.service.Impl;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.model.CommonResult;
import com.io661.extension.model.Steam.*;
import com.io661.extension.service.TransactionAssistantService;
import com.io661.extension.util.User.UserCookieManager;
import lombok.Data;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class TransactionAssistantServiceImpl implements TransactionAssistantService {
    private final CommonHttpUrl httpClient;

    public TransactionAssistantServiceImpl() {
        this.httpClient = new CommonHttpUrl();
    }

    @Override
    public List<SteamInventoryRes> getInventoryList(Integer type, String steamId, Integer limit, Integer subType, boolean onSell, Integer sort, String search) {

        String token = UserCookieManager.readToken();
        try {

            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();

            headers.put("Cookie", "Authorization=" + token);

            String onSellValue = onSell ? "true" : "false";

            // 处理搜索参数，确保不为null
            String searchParam = search != null ? search : "";

            // 对搜索参数进行URL编码
            String encodedSearch = java.net.URLEncoder.encode(searchParam, StandardCharsets.UTF_8);

            String endpoint = "web/inventory?type=" + type + "&steamId=" + steamId + "&limit=" + limit + "&subType=" + subType + "&onSell=" + onSellValue + "&sort=" + sort;

            // 只有当搜索参数不为空时才添加到URL中
            if (!searchParam.isEmpty()) {
                endpoint += "&search=" + encodedSearch;
            }

            String response = httpClient.doGet(endpoint, null, headers);

            System.out.println("库存接口响应" + response);

            // 使用GsonBuilder注册LocalDateTime适配器
            Gson gson = new GsonBuilder()
                    .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter())
                    .create();

            // 解析响应为 SteamInventoryResponse 类型
            com.io661.extension.model.Steam.SteamInventoryResponse inventoryResponse =
                    gson.fromJson(response, com.io661.extension.model.Steam.SteamInventoryResponse.class);

            // 检查响应是否成功
            if (inventoryResponse != null && inventoryResponse.getCode() == 0 &&
                    inventoryResponse.getData() != null && inventoryResponse.getData().getList() != null) {
                return inventoryResponse.getData().getList();
            } else {
                System.out.println("获取库存失败: " + (inventoryResponse != null ? inventoryResponse.getMsg() : "响应为空"));
                return new ArrayList<>();
            }


        }catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return List.of();
    }

    /**
     * 上架物品到市场
     *
     * @param request 上架请求
     * @return 上架响应
     */
    @Override
    public MarketListingResponse listItemsOnMarket(MarketListingRequest request) {
        String token = UserCookieManager.readToken();
        try {
            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);
            headers.put("Content-Type", "application/json");

            // 将请求对象转换为JSON
            Gson gson = new Gson();
            String jsonBody = gson.toJson(request);

            // 发送POST请求
            String endpoint = "web/inventory/listing";
            String response = httpClient.doPost(endpoint, jsonBody, headers);

            System.out.println("上架接口响应: " + response);

            // 解析响应
            MarketListingResponse listingResponse = gson.fromJson(response, MarketListingResponse.class);

            if (listingResponse != null && listingResponse.getCode() == 0) {
                System.out.println("上架成功: " + (listingResponse.getData() != null ?
                        "成功: " + listingResponse.getData().getSuccessCount() +
                        ", 失败: " + listingResponse.getData().getFailCount() : "无数据"));
            } else {
                System.out.println("上架失败: " + (listingResponse != null ? listingResponse.getMsg() : "响应为空"));
            }

            return listingResponse;
        } catch (Exception e) {
            System.out.println("上架物品异常: " + e.getMessage());

            // 创建一个错误响应
            MarketListingResponse errorResponse = new MarketListingResponse();
            errorResponse.setCode(-1);
            errorResponse.setMsg("上架物品异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 变更物品上下架状态
     *
     * @param request 上下架请求
     * @return 上下架响应
     */
    @Override
    public ChangeOnSellStatusRes changeOnSellStatus(ChangeOnSellStatusReq request) {
        String token = UserCookieManager.readToken();
        try {
            httpClient.setAuthToken(token);

            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", "Authorization=" + token);
            headers.put("Content-Type", "application/json");

            // 将请求对象转换为JSON
            Gson gson = new Gson();
            String jsonBody = gson.toJson(request);

            // 打印请求体，用于调试价格问题
            System.out.println("上下架请求体: " + jsonBody);

            // 发送POST请求
            String endpoint = "web/inventory";
            String response = httpClient.doPost(endpoint, jsonBody, headers);

            System.out.println("上下架接口响应: " + response);

            // 解析响应
            CommonResult commonResult =
                gson.fromJson(response, CommonResult.class);

            if (commonResult != null && commonResult.getCode() == 0) {
                System.out.println("上下架操作成功");
                // 从通用响应中提取数据部分
                String dataJson = gson.toJson(commonResult.getData());
                ChangeOnSellStatusRes result = gson.fromJson(dataJson, ChangeOnSellStatusRes.class);
                return result;
            } else {
                System.out.println("上下架操作失败: " + (commonResult != null ? commonResult.getMsg() : "响应为空"));
                return new ChangeOnSellStatusRes();
            }
        } catch (Exception e) {
            System.out.println("上下架操作异常: " + e.getMessage());
            return new ChangeOnSellStatusRes();
        }
    }

    // LocalDateTime的TypeAdapter
    private static class LocalDateTimeAdapter extends TypeAdapter<LocalDateTime> {
        private final DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

        @Override
        public void write(JsonWriter out, LocalDateTime value) throws IOException {
            if (value == null) {
                out.nullValue();
            } else {
                out.value(formatter.format(value));
            }
        }

        @Override
        public LocalDateTime read(JsonReader in) throws IOException {
            if (in.peek() == com.google.gson.stream.JsonToken.NULL) {
                in.nextNull();
                return null;
            }
            String dateStr = in.nextString();
            if (dateStr == null || dateStr.isEmpty()) {
                return null;
            }
            try {
                return LocalDateTime.parse(dateStr, formatter);
            } catch (Exception e) {
                // 尝试其他格式
                try {
                    return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } catch (Exception ex) {
                    System.out.println("解析日期时间失败: " + dateStr);
                    return null;
                }
            }
        }
    }
}
