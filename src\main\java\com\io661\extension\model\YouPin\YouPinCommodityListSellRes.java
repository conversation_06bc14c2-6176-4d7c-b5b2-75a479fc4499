package com.io661.extension.model.YouPin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class YouPinCommodityListSellRes implements Serializable {
    private Integer Code;
    private String Msg;
    private Data_ Data;
    private Integer TotalCount;

    @Data
    public static class Data_ implements Serializable {
        private List<?> adList;
        private CommodityListPageConfigDTO commodityListPageConfigDTO;
        private List<?> UltraLongLeaseCommodityBannerList;
        private List<CommodityList> CommodityList;
        private List<?> PreSaleCommodityBannerList;
        private Object Message;
        private Object StatusCode;
        private Integer CsInspectionVersion;
    }

    @Data
    public static class CommodityListPageConfigDTO implements Serializable {
        private String ultraLongLeaseDayLightUrl;
        private String ultraLongLeaseDayDarkUrl;
        private String presaleDayLightUrl;
        private String presaleDayDarkUrl;
    }

    @Data
    public static class CommodityList implements Serializable {
        private Object discountPrice;
        private Object activityId;
        private Object deductionAmount;
        private Object deductionRate;
        private String transPic;
        private List<?> cashbackConfigs;
        private Integer havePendant;
        private Object pendants;
        private Object actions;
        private Object stickerPremiumInfo;
        private Object autoDeliveryType;
        private Object pendantStyle;
        private Integer paintSeed;
        private Object standardKey;
        private Integer remarkStatus;
        private String leaseDayDesc;
        private Object IsFavorite;
        private Integer OpenSublet;
        private Integer NewOrder;
        private Integer OpenNewOrder;
        private Long Id;
        private Object Type;
        private Integer GameId;
        private Long UserId;
        private Object OldUserId;
        private Object SteamId;
        private String CommodityNo;
        private String CommodityName;
        private Integer TemplateId;
        private Object SteamAssetId;
        private Object SteamClassId;
        private Object SteamInstanceId;
        private Object SteamOfferId;
        private Object SelectId;
        private String IconUrl;
        private String IconUrlLarge;
        private String Price;
        private Object FloorPrice;
        private Object MarkPrice;
        private Object MarkPricePercent;
        private Object Number;
        private Object Attach;
        private String Remark;
        private Object Reason;
        private Object RetryNum;
        private Object Attr;
        private Object Status;
        private Object FrozenTime;
        private Object SuccessTime;
        private Object FailTime;
        private Long PublishTime;
        private Object OfferTime;
        private Object OnShelfTime;
        private Object OffShelfTime;
        private Object UpdateTime;
        private Object Images;
        private Object Img3d;
        private Object StickerRefreshStatus;
        private Integer HaveSticker;
        private Integer HaveBuZhang;
        private Integer HaveNameTag;
        private Integer CanSold;
        private Integer CanLease;
        private String UserNickName;
        private String UserAvatar;
        private Boolean IsMine;
        private Object WeaponTypeName;
        private Object WeaponTypeHashName;
        private Object WeaponTypeIcon;
        private Object WeaponTypeId;
        private String TypeName;
        private String CommodityHashName;
        private String Abrade;
        private String LeaseUnitPrice;
        private Object LongLeaseUnitPrice;
        private Object UltraLongLeaseUnitPrice;
        private String LeaseDeposit;
        private Object OpenLongLease;
        private Object NameTags;
        private Object IsCanSold;
        private Object Rarity;
        private Object RarityColor;
        private Object Quality;
        private Object QualityColor;
        private Object Exterior;
        private Object ExteriorColor;
        private List<?> Stickers;
        private Integer LeaseMaxDays;
        private Object LeaseGiveConfigs;
        private Integer DopplerStatus;
        private Object DopplerName;
        private Object DopplerColor;
        private Integer FadeStatus;
        private Object FadeName;
        private Object FadeColor;
        private Object FadeNumber;
        private Integer IsHardened;
        private Object HardenedName;
        private Object HardenedColor;
        private String SecondDFrontImage;
        private String storeName;
        private Object ZeroRentLeaseStr;
        private Object ZeroRentLeasePrice;
        private Object ZeroRentDepositStr;
        private Object UltraLongLeaseDays;
        private Integer CommodityListType;
        private Object BatchNumContent;
        private Object ClothContent;
        private Object GroupId;
        private List<?> MergeList;
        private List<TagList> TagList;
        private Object ultraRenterSubsidyPrice;
        private Object ultraRenterSubsidyPriceTips;
        private Object maxSubsidyPrice;
        private Object startTime;
        private Object startText;
        private Object deliveryCompensate;
        private Object deliveryCompensateText;
        private Object coolingDays;
        private String unit;
    }

    @Data
    public static class TagList implements Serializable {
        private Integer TagId;
        private String TagDesc;
        private String NormalImgUrl;
        private String DarkImgUrl;
    }
}
