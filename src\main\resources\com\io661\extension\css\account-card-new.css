/* 账号卡片样式 */
.account-card {
    -fx-background-color: white;
    -fx-background-radius: 4px;
    -fx-border-color: #e0e0e0;
    -fx-border-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 3, 0, 0, 1);
    -fx-padding: 8px;
    -fx-margin: 0 0 5px 0;
}

.account-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 5, 0, 0, 2);
    -fx-background-color: #f9f9f9;
}

.account-card.selected {
    -fx-border-color: #6200ee;
    -fx-border-width: 2px;
}

/* 头像样式 */
.avatar-container {
    -fx-min-width: 30px;
    -fx-min-height: 30px;
    -fx-max-width: 30px;
    -fx-max-height: 30px;
}

.avatar-circle {
    -fx-fill: #f0f0f0;
    -fx-stroke: #e0e0e0;
    -fx-stroke-width: 1px;
}

/* 用户名和余额样式 */
.username-label {
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.balance-label {
    -fx-font-size: 12px;
    -fx-text-fill: #4caf50;
    -fx-font-weight: bold;
}

/* 时间标签样式 */
.time-label {
    -fx-font-size: 11px;
    -fx-text-fill: #757575;
    -fx-padding: 2px 0 5px 0;
}

/* 卡片图标按钮样式 */
.card-icon-button {
    -fx-background-color: transparent;
    -fx-padding: 3px;
    -fx-cursor: hand;
}

.card-icon-button:hover {
    -fx-background-color: #f5f5f5;
    -fx-background-radius: 50%;
}

/* 信息标签样式 */
.info-label {
    -fx-font-size: 11px;
    -fx-text-fill: #757575;
}

.info-value {
    -fx-font-size: 11px;
    -fx-text-fill: #333333;
}

/* 复制按钮样式 */
.copy-button {
    -fx-background-color: transparent;
    -fx-padding: 2px;
    -fx-cursor: hand;
}

.copy-button:hover {
    -fx-background-color: #f5f5f5;
    -fx-background-radius: 50%;
}

/* 下拉按钮样式 */
.dropdown-button {
    -fx-background-color: transparent;
    -fx-padding: 2px;
    -fx-cursor: hand;
}

.dropdown-button:hover {
    -fx-background-color: #f5f5f5;
    -fx-background-radius: 50%;
}

/* 卡片操作按钮样式 */
.card-action-button {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333333;
    -fx-padding: 5px 0;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
    -fx-font-size: 12px;
}

.card-action-button:hover {
    -fx-background-color: #e5e5e5;
}
