package com.io661.extension.controller;

import com.io661.extension.model.Steam.SteamRes;
import com.io661.extension.model.Steam.SteamTokenRes;
import com.io661.extension.service.AccountManagerService;
import com.io661.extension.service.Impl.AccountManagerServiceImpl;
import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.input.Clipboard;
import javafx.scene.input.ClipboardContent;
import javafx.scene.web.WebEngine;
import javafx.scene.web.WebView;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.util.Duration;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.File;
import java.net.URL;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * Steam控制器 - 集中管理所有Steam相关功能
 * 整合了所有Steam相关控制器的功能
 */
@Data
public class SteamController implements Initializable {

    // 服务
    private final AccountManagerService accountManagerService;

    //region 通用字段
    // 通用字段
    @Setter
    private Stage stage;

    @FXML
    private Label accountNameLabel;

    @FXML
    private Label tokenLabel;

    @FXML
    private ProgressBar tokenProgressBar;

    @FXML
    private Button copyButton;

    @Setter
    private boolean isQuick = true; // 默认为true，表示查看令牌模式

    @FXML
    private Button closeButton;

    private Timeline tokenRefreshTimeline;
    private Timeline progressUpdateTimeline;

    @Setter
    private String steamId;

    @Setter
    private String accountName;

    private Map<String, Long> tokenMap;
    private String currentToken;
    private long currentTokenExpiry;
    private long tokenDuration = 30000; // 默认30秒

    @FXML
    private PasswordField passwordField;

    @FXML
    private Button confirmButton;

    @FXML
    private Button cancelButton;

    @Getter
    private String password;

    @Getter
    private boolean confirmed = false;

    @FXML
    private WebView webView;

    @FXML
    private Button extractCookiesButton;

    @FXML
    private Label statusLabel;

    @FXML
    private ProgressIndicator progressIndicator;

    @FXML
    private ProgressBar progressBar;

    private WebEngine webEngine;

    @Setter
    private Consumer<Map<String, Map<String, String>>> cookieCallback;
    @Getter
    private Map<String, Map<String, String>> cookieMap;
    // BatchSteamLoginController字段
    @FXML
    private Button backButton;

    //endregion

    //region 批量绑定字段
    @FXML
    private Button uploadMaFileButton;
    @FXML
    private TextArea accountPasswordTextArea;
    @FXML
    private TextArea resultTextArea;
    @FXML
    private Button bindButton;
    private List<File> selectedMaFiles = new ArrayList<>();


    /**
     * 构造函数
     */
    public SteamController() {
        this.accountManagerService = new AccountManagerServiceImpl();
    }

    //endregion

    /**
     * 获取授权令牌
     * 从LoginController获取授权令牌
     */
    public static String getAuthToken() {
        // 从LoginController获取授权令牌
        return LoginController.getAuthToken();
    }

    /**
     * 初始化方法 - 由JavaFX在FXML加载后调用
     */
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // 根据FXML文件的不同，初始化不同的组件
        String fxmlPath = location.getPath();

        if (fxmlPath.contains("steam-token-dialog.fxml")) {
            initializeTokenDialog();
        } else if (fxmlPath.contains("steam-bind-dialog.fxml") || fxmlPath.contains("steam-password-dialog.fxml")) {
            initializePasswordDialog();
        } else if (fxmlPath.contains("batch-steam-login.fxml")) {
            initializeBatchLogin();
        }
    }

    /**
     * 初始化令牌对话框
     */
    private void initializeTokenDialog() {
        // 设置点击令牌标签复制令牌
        if (tokenLabel != null) {
            tokenLabel.setStyle(tokenLabel.getStyle() + "; -fx-cursor: hand;");
            tokenLabel.setOnMouseClicked(event -> handleCopy());
        }

        // 设置点击窗口关闭按钮关闭窗口
        if (closeButton != null) {
            closeButton.setOnAction(event -> handleClose());
        }
    }

    /**
     * 初始化密码对话框
     */
    private void initializePasswordDialog() {
        // 设置回车键确认
        if (passwordField != null) {
            passwordField.setOnAction(event -> handleConfirm());
        }
    }


    /**
     * 初始化批量登录
     */
    private void initializeBatchLogin() {
        // 设置返回按钮点击事件
        if (backButton != null) {
            backButton.setOnAction(event -> handleBack());
        }

        // 设置关闭按钮点击事件
        if (closeButton != null) {
            closeButton.setOnAction(event -> handleClose());
        }

        // 设置上传MaFile按钮点击事件
        if (uploadMaFileButton != null) {
            uploadMaFileButton.setOnAction(event -> handleUploadMaFile());
        }

        // 设置绑定按钮点击事件
        if (bindButton != null) {
            bindButton.setOnAction(event -> handleBind());
        }
    }

    //region 令牌查看功能

    /**
     * 显示Steam令牌对话框
     * @param steamId Steam账号ID
     * @param accountName 账号名称
     * @param isQuick 是否为quick模式
     */
    public void showTokenDialog(String steamId, String accountName, boolean isQuick) {
        try {
            // 创建新的Stage用于显示令牌
            Stage tokenStage = new Stage();
            tokenStage.initModality(Modality.APPLICATION_MODAL);
            tokenStage.setTitle("Steam令牌");
            tokenStage.setResizable(false);
            // 设置窗口无装饰（无标题栏）
            tokenStage.initStyle(javafx.stage.StageStyle.UNDECORATED);

            // 加载令牌对话框FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/steam-token-dialog.fxml"));
            Parent root = loader.load();

            // 获取控制器
            SteamController controller = loader.getController();
            if (controller != null) {
                controller.stage = tokenStage;
                controller.steamId = steamId;
                controller.accountName = accountName;
                controller.isQuick = isQuick;
            }

            // 设置场景
            Scene scene = new Scene(root);
            tokenStage.setScene(scene);

            // 显示窗口并居中
            tokenStage.centerOnScreen();
            tokenStage.show();

            // 加载令牌数据
            if (controller != null) {
                controller.loadTokenData();
            }

        } catch (Exception e) {
            System.err.println("打开令牌窗口失败: " + e.getMessage());
            e.printStackTrace();

            // 显示错误提示
            Platform.runLater(() -> {
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("错误");
                alert.setHeaderText(null);
                alert.setContentText("打开令牌窗口失败: " + e.getMessage());
                alert.showAndWait();
            });
        }
    }

    /**
     * 处理复制按钮点击
     */
    @FXML
    private void handleCopy() {
        if (currentToken != null && !currentToken.isEmpty()) {
            final Clipboard clipboard = Clipboard.getSystemClipboard();
            final ClipboardContent content = new ClipboardContent();
            content.putString(currentToken);
            clipboard.setContent(content);
            System.out.println("令牌已复制到剪贴板: " + currentToken);

            // 显示复制成功提示
            showCopySuccessTooltip();
        }
    }

    /**
     * 显示复制成功提示
     */
    private void showCopySuccessTooltip() {
        if (tokenLabel == null) return;

        // 保存原始样式
        String originalStyle = tokenLabel.getStyle();

        // 设置复制成功样式
        tokenLabel.setStyle(originalStyle + "; -fx-text-fill: #4CAF50;");

        // 创建定时器，1秒后恢复原始样式
        Timeline timeline = new Timeline(
                new KeyFrame(Duration.seconds(1), event -> tokenLabel.setStyle(originalStyle))
        );
        timeline.setCycleCount(1);
        timeline.play();
    }

    /**
     * 加载令牌数据
     */
    public void loadTokenData() {
        if (steamId == null || steamId.isEmpty()) {
            System.out.println("Steam ID为空，无法加载令牌");
            return;
        }

        // 设置账号名称
        if (accountName != null && !accountName.isEmpty() && accountNameLabel != null) {
            accountNameLabel.setText(accountName);
        }

        // 如果不是quick模式，显示提示信息并返回
        if (!isQuick && tokenLabel != null && tokenProgressBar != null) {
            tokenLabel.setText("无法查看");
            tokenProgressBar.setProgress(0);
            return;
        }

        // 获取授权令牌
        String authToken = getAuthToken();
        if (authToken == null || authToken.isEmpty()) {
            System.out.println("未找到授权令牌，无法获取Steam令牌");
            return;
        }

        // 获取Steam令牌
        SteamTokenRes tokenRes = accountManagerService.getSteamToken(steamId, authToken);
        if (tokenRes != null && tokenRes.getData() != null && tokenRes.getData().getTokenMap() != null) {
            tokenMap = new TreeMap<>(tokenRes.getData().getTokenMap());
            System.out.println("成功获取令牌数据: " + tokenMap.size() + " 个令牌");

            // 显示第一个令牌
            updateTokenDisplay();

            // 启动令牌刷新定时器
            startTokenRefreshTimer();
        } else {
            System.out.println("获取令牌失败或令牌为空");
            if (tokenLabel != null) tokenLabel.setText("获取失败");
            if (tokenProgressBar != null) tokenProgressBar.setProgress(0);
        }
    }

    /**
     * 更新令牌显示
     */
    private void updateTokenDisplay() {
        if (tokenMap == null || tokenMap.isEmpty()) {
            return;
        }

        // 获取当前时间
        long currentTime = System.currentTimeMillis();

        // 找到最近的未过期令牌（过期时间最近的）
        currentToken = null;
        currentTokenExpiry = 0;
        long minTimeDifference = Long.MAX_VALUE;

        for (Map.Entry<String, Long> entry : tokenMap.entrySet()) {
            if (entry.getValue() > currentTime) {
                long timeDifference = entry.getValue() - currentTime;
                if (timeDifference < minTimeDifference) {
                    minTimeDifference = timeDifference;
                    currentToken = entry.getKey();
                    currentTokenExpiry = entry.getValue();
                }
            }
        }

        if (currentToken != null && tokenLabel != null) {
            // 更新UI
            Platform.runLater(() -> {
                tokenLabel.setText(currentToken);
            });

            // 计算令牌持续时间
            tokenDuration = currentTokenExpiry - currentTime;

            // 启动进度条更新定时器
            startProgressUpdateTimer();
        }
    }

    /**
     * 启动令牌刷新定时器
     */
    private void startTokenRefreshTimer() {
        if (tokenRefreshTimeline != null) {
            tokenRefreshTimeline.stop();
        }

        tokenRefreshTimeline = new Timeline(
                new KeyFrame(Duration.seconds(30), event -> updateTokenDisplay())
        );
        tokenRefreshTimeline.setCycleCount(Timeline.INDEFINITE);
        tokenRefreshTimeline.play();
    }

    /**
     * 启动进度条更新定时器
     */
    private void startProgressUpdateTimer() {
        if (progressUpdateTimeline != null) {
            progressUpdateTimeline.stop();
        }

        // 每50毫秒更新一次进度条，使动画更流畅
        progressUpdateTimeline = new Timeline(
                new KeyFrame(Duration.millis(50), event -> {
                    if (tokenProgressBar == null) return;

                    long now = System.currentTimeMillis();
                    long remaining = currentTokenExpiry - now;

                    if (remaining <= 0) {
                        // 令牌已过期，更新显示
                        updateTokenDisplay();
                    } else {
                        // 更新进度条 - 反向显示进度（从满到空）
                        double progress = 1.0 - (double) remaining / tokenDuration;

                        // 设置进度条颜色 - 根据剩余时间变化颜色
                        if (remaining < tokenDuration * 0.2) {
                            // 剩余时间少于20%时显示红色
                            tokenProgressBar.setStyle("-fx-accent: #F44336;");
                        } else if (remaining < tokenDuration * 0.5) {
                            // 剩余时间少于50%时显示黄色
                            tokenProgressBar.setStyle("-fx-accent: #FFC107;");
                        } else {
                            // 其他情况显示绿色
                            tokenProgressBar.setStyle("-fx-accent: #4CAF50;");
                        }

                        tokenProgressBar.setProgress(Math.max(0, Math.min(1, progress)));
                    }
                })
        );
        progressUpdateTimeline.setCycleCount(Timeline.INDEFINITE);
        progressUpdateTimeline.play();
    }

    //endregion

    //region 令牌绑定功能

    /**
     * 显示密码输入对话框
     * @param maFileContent .maFile文件内容
     * @param callback 回调函数，参数为密码
     */
    public void showPasswordDialog(String maFileContent, Consumer<String> callback) {
        try {
            // 创建新的Stage用于密码输入
            Stage passwordStage = new Stage();
            passwordStage.initModality(Modality.APPLICATION_MODAL);
            passwordStage.setResizable(false);

            // 加载密码输入对话框FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/steam-bind-dialog.fxml"));
            Parent root = loader.load();

            // 获取控制器
            SteamController controller = loader.getController();
            if (controller != null) {
                controller.stage = passwordStage;
            }

            // 设置场景
            Scene scene = new Scene(root);
            passwordStage.setScene(scene);

            // 显示窗口并等待结果
            passwordStage.showAndWait();

            // 处理结果
            if (controller != null && controller.confirmed) {
                String password = controller.password;

                if (password == null || password.isEmpty()) {
                    // 显示错误提示
                    Alert alert = new Alert(Alert.AlertType.ERROR);
                    alert.setTitle("错误");
                    alert.setHeaderText(null);
                    alert.setContentText("密码不能为空");
                    alert.showAndWait();
                    return;
                }

                // 调用回调函数
                callback.accept(password);
            }
        } catch (Exception e) {
            System.err.println("显示密码输入对话框失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理确认按钮点击
     */
    @FXML
    private void handleConfirm() {
        if (passwordField != null) {
            password = passwordField.getText();
            confirmed = true;
            closeStage();
        }
    }

    /**
     * 设置初始密码
     * @param initialPassword 初始密码
     */
    public void setInitialPassword(String initialPassword) {
        if (passwordField != null && initialPassword != null) {
            passwordField.setText(initialPassword);
        }
    }

    /**
     * 绑定Steam令牌
     * @param maFileContent .maFile文件内容
     * @param password 密码
     * @param token 授权令牌
     * @param callback 回调函数，参数为是否成功
     */
    public void bindSteamToken(String maFileContent, String password, String token, Consumer<Boolean> callback) {
        // 绑定令牌
        boolean success = accountManagerService.bindSteamTokenByMaFile(maFileContent, password, token);

        // 调用回调函数
        callback.accept(success);
    }

    //endregion

    //region 批量绑定功能

    /**
     * 显示批量绑定对话框
     */
    public void showBatchBindDialog() {
        try {
            // 创建新的Stage
            Stage batchBindStage = new Stage();
            batchBindStage.initModality(Modality.APPLICATION_MODAL);
            batchBindStage.setTitle("批量绑定Steam账号");

            // 加载FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/batch-steam-login.fxml"));
            Parent root = loader.load();

            // 获取控制器
            SteamController controller = loader.getController();
            if (controller != null) {
                controller.stage = batchBindStage;
            }

            // 设置场景
            Scene scene = new Scene(root);
            batchBindStage.setScene(scene);

            // 显示窗口
            batchBindStage.show();

        } catch (Exception e) {
            System.err.println("显示批量绑定Steam账号对话框失败: " + e.getMessage());
            e.printStackTrace();

            // 显示错误提示
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("错误");
            alert.setHeaderText(null);
            alert.setContentText("显示批量绑定Steam账号对话框失败: " + e.getMessage());
            alert.showAndWait();
        }
    }

    /**
     * 处理返回按钮点击
     */
    private void handleBack() {
        if (stage != null) {
            stage.close();
        }
    }

    /**
     * 处理上传MaFile按钮点击
     */
    private void handleUploadMaFile() {
        // 创建文件选择器
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("选择.maFile文件");
        fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("Steam认证文件", "*.maFile")
        );

        // 允许多选
        List<File> files = fileChooser.showOpenMultipleDialog(null);
        if (files != null && !files.isEmpty()) {
            selectedMaFiles.addAll(files);
            appendResult("已选择 " + files.size() + " 个.maFile文件");
        }
    }

    /**
     * 处理绑定按钮点击
     */
    private void handleBind() {
        // 获取账号密码列表
        String accountPasswordText = accountPasswordTextArea.getText().trim();
        if (accountPasswordText.isEmpty()) {
            showError("请输入账号密码");
            return;
        }

        // 解析账号密码
        List<AccountPassword> accountPasswords = parseAccountPasswords(accountPasswordText);
        if (accountPasswords.isEmpty()) {
            showError("解析账号密码失败，请确保格式正确");
            return;
        }

        // 检查是否有选择.maFile文件
        if (selectedMaFiles.isEmpty()) {
            showError("请选择.maFile文件");
            return;
        }

        // 检查.maFile文件数量是否与账号密码数量匹配
        if (selectedMaFiles.size() != accountPasswords.size()) {
            showError("账号密码数量与.maFile文件数量不匹配");
            return;
        }

        // 获取授权令牌
        String token = LoginController.getAuthToken();
        if (token == null || token.isEmpty()) {
            showError("未找到授权令牌，无法绑定Steam令牌");
            return;
        }

        // 禁用绑定按钮
        bindButton.setDisable(true);

        // 在后台线程中处理绑定
        new Thread(() -> {
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failCount = new AtomicInteger(0);

            // 遍历账号密码和.maFile文件
            for (int i = 0; i < accountPasswords.size(); i++) {
                AccountPassword accountPassword = accountPasswords.get(i);
                File maFile = selectedMaFiles.get(i);

                try {
                    // 读取.maFile内容
                    String maFileContent = new String(Files.readAllBytes(maFile.toPath()));

                    // 绑定令牌
                    boolean success = accountManagerService.bindSteamTokenByMaFile(maFileContent, accountPassword.password, token);

                    if (success) {
                        successCount.incrementAndGet();
                        appendResult("绑定成功: " + accountPassword.account);
                    } else {
                        failCount.incrementAndGet();
                        appendResult("绑定失败: " + accountPassword.account);
                    }
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    appendResult("绑定异常: " + accountPassword.account + " - " + e.getMessage());
                }
            }

            // 更新UI
            Platform.runLater(() -> {
                // 启用绑定按钮
                bindButton.setDisable(false);

                // 显示结果
                appendResult("绑定完成: 成功 " + successCount.get() + " 个, 失败 " + failCount.get() + " 个");

                // 显示提示
                if (successCount.get() > 0) {
                    showInfo("绑定完成: 成功 " + successCount.get() + " 个, 失败 " + failCount.get() + " 个");
                } else {
                    showError("绑定失败: 所有账号都绑定失败");
                }
            });
        }).start();
    }

    /**
     * 解析账号密码
     * @param text 账号密码文本
     * @return 账号密码列表
     */
    private List<AccountPassword> parseAccountPasswords(String text) {
        List<AccountPassword> result = new ArrayList<>();
        String[] lines = text.split("\\r?\\n");

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) {
                continue;
            }

            String[] parts = line.split("-");
            if (parts.length == 2) {
                String account = parts[0].trim();
                String password = parts[1].trim();
                if (!account.isEmpty() && !password.isEmpty()) {
                    result.add(new AccountPassword(account, password));
                }
            }
        }

        return result;
    }

    /**
     * 添加结果
     * @param message 消息
     */
    private void appendResult(String message) {
        if (resultTextArea != null) {
            Platform.runLater(() -> {
                resultTextArea.appendText(message + "\n");
                // 滚动到底部
                resultTextArea.setScrollTop(Double.MAX_VALUE);
            });
        }
    }

    /**
     * 显示错误提示
     * @param message 错误消息
     */
    private void showError(String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("错误");
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    /**
     * 显示信息提示
     * @param message 信息消息
     */
    private void showInfo(String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("提示");
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    /**
     * 批量绑定Steam令牌
     * @param maFiles .maFile文件列表
     * @param accountPasswords 账号密码列表
     * @param token 授权令牌
     * @param progressCallback 进度回调函数
     * @param resultCallback 结果回调函数
     */
    public void batchBindSteamToken(List<File> maFiles, List<String> accountPasswords, String token,
                                    Consumer<String> progressCallback, Consumer<BatchBindResult> resultCallback) {
        // 在后台线程中处理绑定
        new Thread(() -> {
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failCount = new AtomicInteger(0);

            // 遍历账号密码和.maFile文件
            for (int i = 0; i < accountPasswords.size(); i++) {
                String accountPassword = accountPasswords.get(i);
                File maFile = maFiles.get(i);

                try {
                    // 解析账号密码
                    String[] parts = accountPassword.split("-");
                    if (parts.length != 2) {
                        failCount.incrementAndGet();
                        progressCallback.accept("格式错误: " + accountPassword);
                        continue;
                    }

                    String account = parts[0].trim();
                    String password = parts[1].trim();

                    // 读取.maFile内容
                    String maFileContent = new String(Files.readAllBytes(maFile.toPath()));

                    // 绑定令牌
                    boolean success = accountManagerService.bindSteamTokenByMaFile(maFileContent, password, token);

                    if (success) {
                        successCount.incrementAndGet();
                        progressCallback.accept("绑定成功: " + account);
                    } else {
                        failCount.incrementAndGet();
                        progressCallback.accept("绑定失败: " + account);
                    }
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    progressCallback.accept("绑定异常: " + accountPassword + " - " + e.getMessage());
                }
            }

            // 返回结果
            resultCallback.accept(new BatchBindResult(successCount.get(), failCount.get()));
        }).start();
    }


    /**
     * 显示账号登录对话框
     * @param callback 回调函数，参数为用户名和密码
     */
    public void showAccountLoginDialog(BiConsumer<String, String> callback) {
        try {
            // 创建新的Stage用于密码输入
            Stage passwordStage = new Stage();
            passwordStage.initModality(Modality.APPLICATION_MODAL);
            passwordStage.setResizable(false);

            // 加载密码输入对话框FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/steam-password-dialog.fxml"));
            Parent root = loader.load();

            // 获取控制器
            SteamController controller = loader.getController();
            if (controller != null) {
                controller.stage = passwordStage;
            }

            // 设置场景
            Scene scene = new Scene(root);
            passwordStage.setScene(scene);

            // 显示窗口并等待结果
            passwordStage.showAndWait();

            // 处理结果
            if (controller != null && controller.confirmed) {
                String username = controller.password; // 这里用户名和密码是同一个字段
                String password = controller.password;

                if (password == null || password.isEmpty()) {
                    // 显示错误提示
                    Alert alert = new Alert(Alert.AlertType.ERROR);
                    alert.setTitle("错误");
                    alert.setHeaderText(null);
                    alert.setContentText("密码不能为空");
                    alert.showAndWait();
                    return;
                }

                // 调用回调函数
                callback.accept(username, password);
            }
        } catch (Exception e) {
            System.err.println("显示密码输入对话框失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 登录Steam账号
     * @param cookies 授权令牌
     * @param callback 回调函数，参数为是否成功
     */
    public void loginSteamAccount(String cookies, Consumer<Boolean> callback) {
        // 登录账号
        boolean success = accountManagerService.loginSteamAccount(cookies);

        // 调用回调函数
        callback.accept(success);
    }

    //endregion

    //region 账号登录功能

    /**
     * 解绑Steam令牌
     * @param steamId Steam账号ID
     * @param token 授权令牌
     * @param callback 回调函数，参数为是否成功
     */
    public void unbindSteamToken(String steamId, String token, Consumer<Boolean> callback) {
        // 解绑令牌
        boolean success = accountManagerService.unbindSteamToken(steamId, token);

        // 调用回调函数
        callback.accept(success);
    }

    /**
     * 显示解绑确认对话框
     * @param steamAccount Steam账号
     * @param token 授权令牌
     * @param callback 回调函数，参数为是否成功
     */
    public void showUnbindConfirmDialog(SteamRes.SteamBind steamAccount, String token, Consumer<Boolean> callback) {
        // 显示确认对话框
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle("确认解绑");
        confirmDialog.setHeaderText("确认解绑令牌");
        confirmDialog.setContentText("您确定要解绑 " + steamAccount.getNickname() + " 的令牌吗？");

        // 等待用户确认
        confirmDialog.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                // 用户确认解绑
                unbindSteamToken(steamAccount.getSteamId(), token, callback);
            } else {
                // 用户取消解绑
                callback.accept(false);
            }
        });
    }

    //endregion

    //region 解绑功能

    /**
     * 处理关闭按钮点击
     */
    @FXML
    private void handleClose() {
        // 停止所有定时器
        if (tokenRefreshTimeline != null) {
            tokenRefreshTimeline.stop();
        }
        if (progressUpdateTimeline != null) {
            progressUpdateTimeline.stop();
        }

        // 关闭窗口
        closeStage();
    }

    /**
     * 关闭当前窗口
     */
    private void closeStage() {
        if (stage != null) {
            stage.close();
        }
    }

    //endregion

    //region 通用方法

    /**
     * 账号密码类
     */
    private static class AccountPassword {
        private final String account;
        private final String password;

        public AccountPassword(String account, String password) {
            this.account = account;
            this.password = password;
        }
    }

    /**
     * 批量绑定结果类
     */
    public static class BatchBindResult {
        private final int successCount;
        private final int failCount;

        public BatchBindResult(int successCount, int failCount) {
            this.successCount = successCount;
            this.failCount = failCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailCount() {
            return failCount;
        }
    }

    //endregion
}
